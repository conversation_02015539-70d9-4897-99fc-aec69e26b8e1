import type { FormComponentMapType } from './form-mode-context';
// import hero from "~/assets/svg/hero.svg?url";

import { theme } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router';
import { SSOLogin } from './containers/sso-login';

export default function Login() {
  const { token } = theme.useToken();
  const { t } = useTranslation();
  const [formMode, setFormMode] = useState<FormComponentMapType>('login');

  const [searchParams] = useSearchParams();
  const type = searchParams.get('type');
  useEffect(() => {
    if (type) {
      setFormMode(type as FormComponentMapType);
    }
  }, [type]);

  const providedValue = useMemo(() => ({ formMode, setFormMode }), [formMode, setFormMode]);
  return (
    <div
      style={{
        backgroundColor: token.colorBgContainer,
      }}
    >
      <SSOLogin />
    </div>
  );
}
