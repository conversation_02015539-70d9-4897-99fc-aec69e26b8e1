import { Select } from 'antd';
import { useCallback, useState } from 'react';
import { useEmployee } from '~/views/main/setting/employee/hook/useEmployee';

const { Option } = Select;

interface MemberSelectProps {
  onChange: (value: string) => void;
  isDisable?: boolean;
  value?: string;
}

const MemberSelect = ({ onChange, isDisable = false, value }: MemberSelectProps) => {
  const [limit, setLimit] = useState(50);
  const STEP = 50;
  const { data, isLoading, page, setPage } = useEmployee();

  const bumpLimit = useCallback(() => {
    const next = limit + STEP;
    setLimit(next);
    setPage({
      ...page,
      pageIndex: 1,
      pageSize: next,
    });
  }, [limit, page, setPage]);

  const handlePopupScroll: React.UIEventHandler<HTMLDivElement> = e => {
    const target = e.currentTarget;
    const nearBottom = target.scrollTop + target.clientHeight >= target.scrollHeight - 4;

    if (nearBottom && !isLoading) {
      bumpLimit();
    }
  };

  const handleChange = (value: string) => {
    onChange(value);
  };

  return (
    <Select
      loading={isLoading}
      showSearch
      placeholder="Chọn nhân viên"
      onPopupScroll={handlePopupScroll}
      onChange={handleChange}
      disabled={isDisable}
      value={value}
    >
      {data.length > 0 && data.map((item: any) => (
        <Option key={item.id} value={item.id}>
          {item.fullName}
        </Option>
      ))}
    </Select>
  );
};

export default MemberSelect;
