import { BasicContent, FormAvatarItem } from '~/components';
import { useAccountStore } from '~/store';

import { ProForm, ProFormDigit, ProFormText, ProFormTextArea } from '@ant-design/pro-components';
import { Form, Input } from 'antd';
import { useTranslation } from 'react-i18next';

export default function Profile() {
  const { t } = useTranslation();
  const currentAccount = useAccountStore();
  const getAvatarURL = () => {
    if (currentAccount) {
      if (currentAccount.avatar) {
        return currentAccount.avatar;
      }
      const url = 'https://avatar.vercel.sh/blur.svg?text=2';
      return url;
    }
    return '';
  };

  const handleFinish = async () => {
    window.$message?.success('Basic information updated successfully');
  };

  return (
    <BasicContent className="max-w-md ml-10">
      <h3>{t('form.profile.title')}</h3>
      <ProForm
        layout="vertical"
        onFinish={handleFinish}
        initialValues={{
          ...currentAccount,
          avatar: getAvatarURL(),
        }}
        requiredMark
      >
        <Form.Item
          name="avatar"
          label={t('form.avatar.label')}
          rules={[
            {
              required: true,
              message: t('form.avatar.required'),
            },
          ]}
        >
          <FormAvatarItem />
        </Form.Item>
        <ProFormText
          name="username"
          label={t('form.username.label')}
          rules={[
            {
              required: true,
              message: t('form.username.required'),
            },
          ]}
        />
        <ProFormText
          name="fullName"
          label={t('form.fullname.label')}
          rules={[
            {
              required: true,
              message: t('form.fullname.required'),
            },
          ]}
        />
        <ProFormDigit
          name="phoneNumber"
          label={t('form.phonenumber.label')}
          rules={[
            {
              required: true,
              message: t('form.phonenumber.required'),
            },
          ]}
        >
          <Input type="tel" allowClear />
        </ProFormDigit>
        <ProFormTextArea allowClear name="description" label={t('form.description.label')} />
      </ProForm>
    </BasicContent>
  );
}
