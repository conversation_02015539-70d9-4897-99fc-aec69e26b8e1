/* eslint-disable array-callback-return */
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Col, Collapse, DatePicker, Form, Input, Row, Select } from 'antd';
import { FC, useCallback } from 'react';
import { enumData } from '~/common/enums/enumData';

interface ISelectOption {
  value: any;
  name: string;
}

export interface IFilter {
  key: string;
  name: string;
  type: string;
  selectOptions?: ISelectOption[];
  onSearch?: (value: string) => void;
}

interface IProps {
  onFilter: (values: any) => void;
  onReset: () => void;
  isLoading: boolean;
  filters: IFilter[];
  styles?: React.CSSProperties;
}

const { Panel } = Collapse;

const BaseFilter: FC<IProps> = (props: IProps) => {
  const { onFilter, onReset, isLoading, filters, styles } = props;
  const [form] = Form.useForm();

  const handleFilter = useCallback(() => {
    onFilter(form.getFieldsValue());
  }, [form, onFilter]);

  const handleReset = useCallback(() => {
    form.resetFields();
    onReset();
  }, [form, onReset]);

  // Layout responsive cho từng field
  const colProps = {
    xs: 24,
    sm: 12,
    md: 8,
    lg: 6,
  } as const;

  // Style chung 100% width cho control
  const fullWidth = { width: '100%' } as const;

  return (
    <Collapse style={styles} className="mb-5">
      <Panel header="Tìm kiếm" key="0">
        <Form form={form} layout="vertical" size="middle">
          <Row gutter={[16, 8]}>
            {filters.map((filter, index) => {
              switch (filter.type) {
                case enumData.FILTER_TYPE.INPUT.key:
                  return (
                    <Col key={index} {...colProps}>
                      <Form.Item label={`${filter.name} :`} name={filter.key}>
                        <Input
                          size="middle"
                          allowClear
                          placeholder={`Nhập ${filter.name}`}
                          style={fullWidth}
                          inputMode="text"
                        />
                      </Form.Item>
                    </Col>
                  );

                case enumData.FILTER_TYPE.SELECT.key:
                  return (
                    <Col key={index} {...colProps}>
                      <Form.Item label={`${filter.name} :`} name={filter.key}>
                        <Select
                          size="middle"
                          placeholder={`Chọn ${filter.name}`}
                          allowClear
                          style={fullWidth}
                          dropdownMatchSelectWidth={false}
                          listHeight={256}
                        >
                          {filter.selectOptions?.map(item => (
                            <Select.Option key={item.value}>{item.name}</Select.Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                  );
                case enumData.FILTER_TYPE.SELECT_SEARCH.key:
                  return (
                    <Col key={index} {...colProps}>
                      <Form.Item label={`${filter.name} :`} name={filter.key}>
                        <Select
                          showSearch
                          size="middle"
                          placeholder={`Chọn ${filter.name}`}
                          allowClear
                          style={fullWidth}
                          dropdownMatchSelectWidth={false}
                          listHeight={256}
                          optionFilterProp="children"
                        >
                          {filter.selectOptions?.map(item => (
                            <Select.Option key={item.value} value={item.value}>
                              {item.name}
                            </Select.Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                  );

                case enumData.FILTER_TYPE.DATE.key:
                  return (
                    <Col key={index} {...colProps}>
                      <Form.Item label={`${filter.name} :`} name={filter.key}>
                        <DatePicker
                          size="middle"
                          placeholder={`Chọn ${filter.name}`}
                          format="YYYY-MM-DD"
                          style={fullWidth}
                          allowClear
                        />
                      </Form.Item>
                    </Col>
                  );

                case enumData.FILTER_TYPE.DATE_RANGE.key:
                  return (
                    <Col key={index} {...colProps}>
                      <Form.Item label={`${filter.name} :`} name={filter.key}>
                        <DatePicker.RangePicker
                          size="middle"
                          placeholder={['Từ ngày', 'Đến ngày']}
                          format="YYYY-MM-DD"
                          style={fullWidth}
                          allowClear
                        />
                      </Form.Item>
                    </Col>
                  );

                default:
                  return null;
              }
            })}
          </Row>

          <Row gutter={[16, 8]} style={{ marginTop: 10, justifyContent: 'center' }}>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Button
                type="primary"
                size="middle"
                htmlType="button"
                onClick={handleFilter}
                loading={isLoading}
                icon={<SearchOutlined />}
                style={{ width: '100%' }}
              >
                Tìm kiếm
              </Button>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Button
                type="default"
                size="middle"
                htmlType="button"
                onClick={handleReset}
                icon={<ReloadOutlined />}
                style={{ width: '100%' }}
              >
                Làm mới
              </Button>
            </Col>
          </Row>
        </Form>
      </Panel>
    </Collapse>
  );
};

export default BaseFilter;
