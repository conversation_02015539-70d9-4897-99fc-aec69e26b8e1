import { rootApiConnector } from '~/connectors';
import { UpdateStatusQuotationReq } from '../sales-managerment/quotation-product/types';
import { IQuotation, IQuotationList, IQuotationUpdate } from './types';

export const ENDPOINT_QUOTATION_ITEM = {
  CREATE: `api/client/quote/create`.trim(),
  LIST: `api/client/quote/list`.trim(),
  DETAIL: `api/client/quote/detail`.trim(),
  UPDATE: `api/client/quote/update`.trim(),
  UPDATE_STATUS: `api/client/quote/update-status`.trim(),
};
class QuotationItemApi {
  create = (body: IQuotation) => {
    return rootApiConnector.post(ENDPOINT_QUOTATION_ITEM.CREATE, body);
  };

  list = (params: IQuotationList) => {
    return rootApiConnector.get(ENDPOINT_QUOTATION_ITEM.LIST, params);
  };

  detail = (params: { id: string }) => {
    return rootApiConnector.get(ENDPOINT_QUOTATION_ITEM.DETAIL, params);
  };

  update = (body: IQuotationUpdate) => {
    return rootApiConnector.post(ENDPOINT_QUOTATION_ITEM.UPDATE, body);
  };

  updateStatus = (body: UpdateStatusQuotationReq) => {
    return rootApiConnector.post(ENDPOINT_QUOTATION_ITEM.UPDATE_STATUS, body);
  };
}
export const quotationItemApi = new QuotationItemApi();
