import { rootApiConnector } from '~/connectors';
import { IContact, IContactList } from './types';

export const ENDPOINT_CONTACT = {
  LIST: 'api/client/contact/list',
  CREATE: 'api/client/contact/create',
  UPDATE: 'api/client/contact/update',
  DETAIL: 'api/client/contact/detail',
  DELETE: 'api/client/contact/delete',
};
class ContactApi {
  //list
  list = (params: IContactList) => {
    return rootApiConnector.get(ENDPOINT_CONTACT.LIST, params);
  };

  //create
  create = (params: IContact) => {
    return rootApiConnector.post(ENDPOINT_CONTACT.CREATE, params);
  };

  //update
  update = (params: IContact) => {
    return rootApiConnector.post(ENDPOINT_CONTACT.UPDATE, params);
  };

  //detail
  detail = (id: { id: string }) => {
    return rootApiConnector.post(ENDPOINT_CONTACT.DETAIL, id);
  };

  //delete
  delete = (id: { id: string }) => {
    return rootApiConnector.post(ENDPOINT_CONTACT.DELETE, id);
  };
}
export const contactApi = new ContactApi();
