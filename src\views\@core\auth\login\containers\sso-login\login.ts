// import { Form, message } from 'antd';
// import { useState } from 'react';
// import { useTranslation } from 'react-i18next';
// import { useNavigate, useSearchParams } from 'react-router';

// import { useToat } from '~/hooks';
// import { useAuthStore } from '~/store';

// export const useLogin = () => {
//   const [loading, setLoading] = useState(false);
//   const [passwordLoginForm] = Form.useForm();
//   const { t } = useTranslation();
//   const [messageLoadingApi, contextLoadingHolder] = message.useMessage();
//   const toast = useToat();
//   const [searchParams] = useSearchParams();
//   const navigate = useNavigate();
//   const login = useAuthStore(state => state.login);

//   const handleFinish = async (values: any) => {
//     setLoading(true);
//     messageLoadingApi?.loading(t('authority.loginInProgress'), 0);

//     try {
//       await login(values);
//       messageLoadingApi?.destroy();
//       toast.success('authority.loginSuccess');
//       const redirect = searchParams.get('redirect');
//       if (redirect) {
//         navigate(`/${redirect.slice(1)}`);
//       } else {
//         navigate(import.meta.env.VITE_BASE_HOME_PATH);
//       }
//     } catch (error) {
//       messageLoadingApi?.destroy();
//       toast.handleError(error);
//       setTimeout(() => {
//         toast.destroy();
//         setLoading(false);
//       }, 1000);
//     }
//     setLoading(false);
//   };

//   return {
//     loading,
//     passwordLoginForm,
//     contextLoadingHolder,
//     handleFinish,
//     t,
//   };
// };
