import { PlusOutlined, SaveOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd';
import { Button, Col, DatePicker, Form, Input, Row, Select, Upload, message } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useState } from 'react';
import { ComplaintPriorityLabels, ComplaintTypeLabels, ICreateComplaintReq } from '~/api/customer-care/complaint/types';
import BaseModal from '~/components/base-modal';

const { Option } = Select;
const { TextArea } = Input;

interface CreateComplaintModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

// Dữ liệu mẫu cho khách hàng
const mockCustomers = [
  { id: '1', name: 'Công ty TNHH ABC' },
  { id: '2', name: 'Công ty TNHH XYZ' },
  { id: '3', name: '<PERSON>ông ty C<PERSON> phần DEF' },
  { id: '4', name: '<PERSON><PERSON><PERSON> nghiệp tư nhân GHI' },
];

// Dữ liệu mẫu cho tỉnh/thành phố
const mockProvinces = [
  { code: '01', name: 'Hồ Chí Minh' },
  { code: '02', name: 'Hà Nội' },
  { code: '03', name: 'Đà Nẵng' },
  { code: '04', name: 'Cần Thơ' },
];

// Dữ liệu mẫu cho quận/huyện
const mockDistricts = [
  { code: '001', name: 'Quận 1', provinceCode: '01' },
  { code: '002', name: 'Quận 2', provinceCode: '01' },
  { code: '003', name: 'Quận Ba Đình', provinceCode: '02' },
  { code: '004', name: 'Quận Hoàn Kiếm', provinceCode: '02' },
];

// Dữ liệu mẫu cho phường/xã
const mockWards = [
  { code: '00001', name: 'Phường Bến Nghé', districtCode: '001' },
  { code: '00002', name: 'Phường Bến Thành', districtCode: '001' },
  { code: '00003', name: 'Phường Thảo Điền', districtCode: '002' },
  { code: '00004', name: 'Phường An Phú', districtCode: '002' },
];

// Dữ liệu mẫu cho nhân viên
const mockEmployees = [
  { id: '1', name: 'Nguyễn Văn A' },
  { id: '2', name: 'Trần Thị B' },
  { id: '3', name: 'Lê Văn C' },
  { id: '4', name: 'Phạm Thị D' },
];

const CreateComplaintModal = ({ open, onClose, onSuccess }: CreateComplaintModalProps) => {
  const [form] = useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedProvinceCode, setSelectedProvinceCode] = useState<string | null>(null);
  const [selectedDistrictCode, setSelectedDistrictCode] = useState<string | null>(null);

  const handleSave = async (values: any) => {
    if (!values) return;

    setIsSubmitting(true);

    const body: ICreateComplaintReq = {
      ...values,
      startDate: values.startDate.format('YYYY-MM-DD'),
      dueDate: values.dueDate.format('YYYY-MM-DD'),
      media: fileList.map(file => file.url),
    };

    // Giả lập gọi API
    setTimeout(() => {
      console.log('Dữ liệu khiếu nại đã tạo:', body);
      message.success('Tạo khiếu nại thành công');
      onClose();
      onSuccess?.();
      // Reset form và file list
      form.resetFields();
      setFileList([]);
      setIsSubmitting(false);
      setSelectedProvinceCode(null);
      setSelectedDistrictCode(null);
    }, 1000);
  };

  // Xử lý upload hình ảnh
  const handleUploadChange = ({ fileList }: { fileList: UploadFile[] }) => {
    setFileList(fileList);
  };

  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('Chỉ được upload file hình ảnh!');
      return false;
    }

    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error('Hình ảnh phải nhỏ hơn 5MB!');
      return false;
    }

    return true;
  };

  // Xử lý khi chọn tỉnh/thành phố
  const handleProvinceChange = (value: string) => {
    setSelectedProvinceCode(value);
    setSelectedDistrictCode(null);
    form.setFieldsValue({ districtCode: undefined, wardCode: undefined });
  };

  // Xử lý khi chọn quận/huyện
  const handleDistrictChange = (value: string) => {
    setSelectedDistrictCode(value);
    form.setFieldsValue({ wardCode: undefined });
  };

  // Lọc quận/huyện theo tỉnh/thành phố đã chọn
  const filteredDistricts = selectedProvinceCode
    ? mockDistricts.filter(district => district.provinceCode === selectedProvinceCode)
    : [];

  // Lọc phường/xã theo quận/huyện đã chọn
  const filteredWards = selectedDistrictCode
    ? mockWards.filter(ward => ward.districtCode === selectedDistrictCode)
    : [];

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );

  const modalContent = (
    <Form form={form} layout="vertical" onFinish={handleSave}>
      {/* Thông tin cơ bản */}
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item label="Tiêu đề" name="title" rules={[{ required: true, message: 'Vui lòng nhập tiêu đề' }]}>
            <Input placeholder="Nhập tiêu đề khiếu nại" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="Loại khiếu nại"
            name="type"
            rules={[{ required: true, message: 'Vui lòng chọn loại khiếu nại' }]}
          >
            <Select placeholder="Chọn loại khiếu nại">
              {Object.entries(ComplaintTypeLabels).map(([value, label]) => (
                <Option key={value} value={value}>
                  {label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item label="Mô tả" name="description" rules={[{ required: true, message: 'Vui lòng nhập mô tả' }]}>
            <TextArea rows={3} placeholder="Nhập mô tả chi tiết khiếu nại" />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="Mức độ ưu tiên"
            name="priority"
            rules={[{ required: true, message: 'Vui lòng chọn mức độ ưu tiên' }]}
          >
            <Select placeholder="Chọn mức độ ưu tiên">
              {Object.entries(ComplaintPriorityLabels).map(([value, label]) => (
                <Option key={value} value={value}>
                  {label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="Mã khách hàng"
            name="customerId"
            rules={[{ required: true, message: 'Vui lòng chọn khách hàng' }]}
          >
            <Select placeholder="Chọn khách hàng">
              {mockCustomers.map(customer => (
                <Option key={customer.id} value={customer.id}>
                  {customer.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>

      {/* Thông tin địa chỉ */}
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="Địa chỉ khiếu nại"
            name="complaintAddress"
            rules={[{ required: true, message: 'Vui lòng nhập địa chỉ khiếu nại' }]}
          >
            <Input placeholder="Nhập địa chỉ khiếu nại" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Địa chỉ" name="address" rules={[{ required: true, message: 'Vui lòng nhập địa chỉ' }]}>
            <Input placeholder="Nhập địa chỉ" />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={8}>
          <Form.Item label="Tỉnh/Thành phố" name="provinceCode">
            <Select placeholder="Chọn tỉnh/thành phố" onChange={handleProvinceChange}>
              {mockProvinces.map(province => (
                <Option key={province.code} value={province.code}>
                  {province.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="Quận/Huyện" name="districtCode">
            <Select placeholder="Chọn quận/huyện" onChange={handleDistrictChange} disabled={!selectedProvinceCode}>
              {filteredDistricts.map(district => (
                <Option key={district.code} value={district.code}>
                  {district.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="Phường/Xã" name="wardCode">
            <Select placeholder="Chọn phường/xã" disabled={!selectedDistrictCode}>
              {filteredWards.map(ward => (
                <Option key={ward.code} value={ward.code}>
                  {ward.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>

      {/* Thông tin nhân viên */}
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="NV theo dõi/giám sát"
            name="supervisorId"
            rules={[{ required: true, message: 'Vui lòng chọn nhân viên giám sát' }]}
          >
            <Select placeholder="Chọn nhân viên giám sát">
              {mockEmployees.map(employee => (
                <Option key={employee.id} value={employee.id}>
                  {employee.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="NV được phân công"
            name="assignedStaffId"
            rules={[
              {
                required: true,
                message: 'Vui lòng chọn nhân viên được phân công',
              },
            ]}
          >
            <Select placeholder="Chọn nhân viên được phân công">
              {mockEmployees.map(employee => (
                <Option key={employee.id} value={employee.id}>
                  {employee.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>

      {/* Thông tin thời gian */}
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="Ngày bắt đầu"
            name="startDate"
            rules={[{ required: true, message: 'Vui lòng chọn ngày bắt đầu' }]}
          >
            <DatePicker style={{ width: '100%' }} placeholder="Chọn ngày bắt đầu" format="DD/MM/YYYY" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="Ngày hết hạn"
            name="dueDate"
            rules={[{ required: true, message: 'Vui lòng chọn ngày hết hạn' }]}
          >
            <DatePicker style={{ width: '100%' }} placeholder="Chọn ngày hết hạn" format="DD/MM/YYYY" />
          </Form.Item>
        </Col>
      </Row>

      {/* Hình ảnh đính kèm */}
      <Form.Item label="Hình ảnh đính kèm" name="media">
        <Upload
          listType="picture-card"
          fileList={fileList}
          onChange={handleUploadChange}
          beforeUpload={beforeUpload}
          multiple
          accept="image/*"
          customRequest={({ file, onSuccess }) => {
            // Giả lập upload file
            setTimeout(() => {
              const fakeUrl = URL.createObjectURL(file as File);
              const fakeResponse = {
                Location: fakeUrl,
                name: (file as File).name,
              };
              if (onSuccess) onSuccess(fakeResponse);
            }, 500);
          }}
        >
          {fileList.length >= 8 ? null : uploadButton}
        </Upload>
      </Form.Item>

      <div
        style={{
          textAlign: 'right',
          marginTop: 24,
          borderTop: '1px solid #f0f0f0',
          paddingTop: 16,
        }}
      >
        <Button onClick={onClose} style={{ marginRight: 8 }}>
          Hủy
        </Button>
        <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={isSubmitting}>
          Tạo khiếu nại
        </Button>
      </div>
    </Form>
  );

  return (
    <BaseModal
      open={open}
      onClose={onClose}
      title="Tạo khiếu nại mới"
      description="Thêm khiếu nại mới vào hệ thống"
      childrenBody={modalContent}
    />
  );
};

export default CreateComplaintModal;
