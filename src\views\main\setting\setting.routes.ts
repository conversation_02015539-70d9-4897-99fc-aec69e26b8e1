import { ContainerLayout } from '~/layout';
import type { AppRouteRecordRaw } from '~/router/types';

import { HomeOutlined } from '@ant-design/icons';
import { createElement } from 'react';
import { $t } from '~/locales';
import { SETTING_MENU_ORDER } from '~/views/main/main-menu.order';
import CatalogView from './catalog';
import CatalogItemView from './catalog-item';
import { DepartmentView } from './department';
import { EmployeeView } from './employee';
import { PermissionsView } from './permissions';
import {
  SETTING_CATALOG_ITEM_PATH,
  SETTING_CATALOG_PATH,
  SETTING_CONTRACT_TEMPLATE_PATH,
  SETTING_DEPARTMENT_PATH,
  SETTING_EMPLOYEE_PATH,
  SETTING_PATH,
  SETTING_PERMISSION_PATH,
} from './setting.path';
import ContractTemplateView from './template-contract';

const routes: AppRouteRecordRaw[] = [
  {
    path: SETTING_PATH,
    Component: ContainerLayout,
    handle: {
      order: SETTING_MENU_ORDER.SETTING,
      title: $t('common.menu.setting'),
      icon: createElement(HomeOutlined),
    },
    children: [
      {
        path: SETTING_CATALOG_PATH,
        Component: CatalogView,
        handle: {
          title: $t('common.menu.catalog'),
          icon: createElement(HomeOutlined),
        },
      },
      {
        path: SETTING_CATALOG_ITEM_PATH,
        Component: CatalogItemView,
        handle: {
          title: $t('common.menu.catalog_item'),
          icon: createElement(HomeOutlined),
        },
      },
      {
        path: SETTING_EMPLOYEE_PATH,
        Component: EmployeeView,
        handle: {
          title: $t('common.menu.employee'),
          icon: createElement(HomeOutlined),
        },
      },
      {
        path: SETTING_DEPARTMENT_PATH,
        Component: DepartmentView,
        handle: {
          title: $t('common.menu.department'),
          icon: createElement(HomeOutlined),
        },
      },
      {
        path: SETTING_PERMISSION_PATH,
        Component: PermissionsView,
        handle: {
          title: $t('common.menu.permission'),
          icon: createElement(HomeOutlined),
        },
      },
      {
        path: SETTING_CONTRACT_TEMPLATE_PATH,
        Component: ContractTemplateView,
        handle: {
          title: $t('common.menu.contract_template'),
          icon: createElement(HomeOutlined),
        },
      },
    ],
  },
];

export default routes;
