/* @layer antd, editor; */

/* simplebar */
@import url(simplebar-react/dist/simplebar.min.css) layer(simplebar);

/* 将 Tailwind CSS 的基础样式（也称为 "base" 样式）导入到当前文件中，包括一些基本的 HTML 元素样式、重置元素默认样式等。 */
@import 'tailwindcss/base';

/* 导入 Tailwind CSS 的组件样式，包括预定义的按钮、表格、表单、卡片等组件样式。 */
@import 'tailwindcss/components';

/* 导入 Tailwind CSS 的实用类，这些类通常用于添加与布局、间距、响应式设计等相关的样式，使得可以快速构建出复杂的页面 */
@import 'tailwindcss/utilities';

/* simplebar */
@layer simplebar {
  .simplebar-scrollbar::before {
    background-color: #909399;
  }

  .simplebar-content {
    height: 100%;
  }
}

html.color-blind-mode {
  @apply invert;
}

html.gray-mode {
  @apply grayscale;
}
.rich-text-editor .ql-editor.ql-blank::before {
  color: #999; /* <PERSON><PERSON>u xám cho placeholder thay vì màu đen */
  font-style: italic;
}
.custom-drawer .ant-drawer-body {
  padding: 24px;
  overflow: hidden; /* Ẩn scrollbar gốc */
}

.custom-drawer-content {
  height: 100%;
  overflow-y: auto;
  margin-right: -16px; /* Đưa nội dung sát bên phải */
  padding-right: 10px; /* Tạo khoảng cách cho scrollbar nhưng không làm lùi nội dung */
}

/* Định dạng scrollbar cho Chrome/Safari */
.custom-drawer-content::-webkit-scrollbar {
  width: 6px;
}

.custom-drawer-content::-webkit-scrollbar-track {
  background: #555555;
  border-radius: 10px;
}

.custom-drawer-content::-webkit-scrollbar-thumb {
  background: #1101c8;
  border-radius: 10px;
}

.custom-drawer-content::-webkit-scrollbar-thumb:hover {
  background: #0a0080;
}

/* Hỗ trợ Firefox */
.custom-drawer-content {
  scrollbar-width: thin;
  scrollbar-color: #1101c8 #555555;
}
