export namespace NSAccount {
  export enum EStatus {
    INACTIVE = 'INACTIVE',
    ACTIVE = 'ACTIVE',
    LOCKED = 'LOCKED',
  }
  export enum EType {
    TENANT_MASTER = 'TENANT_MASTER', // root
    TENANT_USER = 'TENANT_USER', // user con của tenant
    CUSTOMER = 'CUSTOMER', // khách hàng của tenant
  }

  export enum EAdminType {
    SUPER_ADMIN = 'SUPER_ADMIN',
    ADMIN = 'ADMIN',
  }

  export const ESTATUS_LOCALE_LABEL = {
    [EStatus.ACTIVE]: 'enum.NSAccount.EStatus.ACTIVE',
    [EStatus.INACTIVE]: 'enum.NSAccount.EStatus.INACTIVE',
    [EStatus.LOCKED]: 'enum.NSAccount.EStatus.LOCKED',
  };

  export const ESTATUS_LABEL_COLOR = {
    [EStatus.ACTIVE]: 'green',
    [EStatus.INACTIVE]: 'red',
    [EStatus.LOCKED]: 'orange',
  };
  export const ETYPE_LOCALE_LABEL = {
    [EType.TENANT_MASTER]: 'enum.NSAccount.EType.TENANT_MASTER',
    [EType.TENANT_USER]: 'enum.NSAccount.EType.TENANT_USER',
    [EType.CUSTOMER]: 'enum.NSAccount.EType.CUSTOMER',
  };
  export const ETYPE_LABEL_COLOR = {
    [EType.TENANT_MASTER]: 'green',
    [EType.TENANT_USER]: 'blue',
    [EType.CUSTOMER]: 'orange',
  };
}
