import { ContainerLayout } from '~/layout';
import type { AppRouteRecordRaw } from '~/router/types';

import { HomeOutlined } from '@ant-design/icons';
import { createElement } from 'react';
import { $t } from '~/locales';
import { MARKETING_CAMPAIGN_MENU_ORDER } from '~/views/main/main-menu.order';
import { CampaignView } from './campaign';
import { CampaignEditComponent } from './campaign/components/campaign-edit-component';
import {
  MARKETING_CAMPAIGN_LIST_EDIT_PATH,
  MARKETING_CAMPAIGN_LIST_PATH,
  MARKETING_CAMPAIGN_PATH,
  MARKETING_CAMPAIGN_REPORT_BIRTHDAY_PATH,
  MARKETING_CAMPAIGN_REPORT_ZNS_PATH,
  MARKETING_CAMPAIGN_TARGET_PATH,
} from './marketing-campaign.path';
import { ReportBirthdayView } from './report-birthday';
import { MarketingReportZNSView } from './report-zns';
import { TargetView } from './target';

const routes: AppRouteRecordRaw[] = [
  {
    path: MARKETING_CAMPAIGN_PATH,
    Component: ContainerLayout,
    handle: {
      order: MARKETING_CAMPAIGN_MENU_ORDER.MARKETING_CAMPAIGN,
      title: $t('common.menu.marketing_campaign'),
      icon: createElement(HomeOutlined),
    },
    children: [
      {
        path: MARKETING_CAMPAIGN_LIST_PATH,
        Component: CampaignView,
        handle: {
          title: $t('common.menu.marketing_campaign_list'),
          icon: createElement(HomeOutlined),
        },
      },
      {
        path: MARKETING_CAMPAIGN_LIST_EDIT_PATH,
        index: true,
        Component: CampaignEditComponent,
        handle: {
          title: $t('common.menu.marketing_campaign_edit'),
          icon: createElement(HomeOutlined),
        },
      },
      {
        path: MARKETING_CAMPAIGN_TARGET_PATH,
        Component: TargetView,
        handle: {
          title: $t('common.menu.marketing_campaign_target'),
          icon: createElement(HomeOutlined),
        },
      },
      {
        path: MARKETING_CAMPAIGN_REPORT_BIRTHDAY_PATH,
        Component: ReportBirthdayView,
        handle: {
          title: $t('common.menu.marketing_campaign_report_birthday'),
          icon: createElement(HomeOutlined),
        },
      },
      {
        path: MARKETING_CAMPAIGN_REPORT_ZNS_PATH,
        Component: MarketingReportZNSView,
        handle: {
          title: $t('common.menu.marketing_campaign_report_zns'),
          icon: createElement(HomeOutlined),
        },
      },
    ],
  },
];

export default routes;
