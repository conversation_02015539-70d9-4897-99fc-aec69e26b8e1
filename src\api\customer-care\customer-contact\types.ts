import { PrimaryBaseEntity } from '~/@core/dto/base.dto';

export interface IComplaintResponse extends PrimaryBaseEntity {
  data: ICustomerContact[];
  total: number;
}

export interface ICustomerContact {
  id: string;
  title: string;
  description: string;
  type: string;
  status: string;
  visitLocation: string;
  customerCode: string;
  sapCode: string;
  address: string;
  supervisor: string;
  assignedEmployee: string;
  model: string;
  dueDate: string;
  actualDate: string;
  checkInDate: string;
  checkOutTime: string;
  messageStatus: string;
}
