import { IPageRequest } from '~/api/@common';
import { customerAddressApi } from '~/api/customer-address';
import { CustomerAddressItemDto, ListCustomerAddressDto } from '~/api/customer-address/types';

const useCustomerAddress = () => {
  const createCustomerAddress = async (data: CustomerAddressItemDto) => {
    return customerAddressApi.create(data);
  };

  const updateCustomerAddress = async (data: CustomerAddressItemDto) => {
    return customerAddressApi.update(data);
  };

  //setActive
  const setActiveCustomerAddress = async (id: string) => {
    return customerAddressApi.setActive({ id });
  };

  //list
  const listCustomerAddress = async (params: ListCustomerAddressDto) => {
    return customerAddressApi.list(params);
  };

  const setDefaultCustomerAddress = async (id: string) => {
    return customerAddressApi.setDefault({ id });
  };

  return {
    createCustomerAddress,
    updateCustomerAddress,
    setActiveCustomerAddress,
    listCustomerAddress,
    setDefaultCustomerAddress,
  };
};

export default useCustomerAddress;
