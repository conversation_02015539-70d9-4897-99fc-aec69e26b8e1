<!doctype html>
<html lang="en">

<head>
	<meta charset="utf-8" />
	<link rel="icon" href="/favicon.ico" />
	<meta name="viewport" content="width=device-width, initial-scale=1" />
	<meta name="theme-color" content="#000000" />
	<meta name="description" content="Web site created using Vite React App" />
	<link rel="apple-touch-icon" href="/logo192.png" />
	<!--
			manifest.json provides metadata used when your web app is installed on a
			user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
		-->
	<link rel="manifest" href="/manifest.json" />
	<!-- Inject the VITE_GLOB_APP_TITLE variable by Vite, configured in the .env file -->
	<title>%VITE_GLOB_APP_TITLE%</title>
</head>

<body>
	<noscript>You need to enable JavaScript to run this app.</noscript>
	<div id="root"></div>
	<script type="module" src="/src/index.tsx"></script>
</body>

</html>
