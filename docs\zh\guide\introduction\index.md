# 介绍 {#instroduction}

react-antd-admin 是一个基于 React Hooks、Vite、TypeScript 和 antd 搭建的**中后台**解决方案。它开箱即用内置权限校验、动态菜单、多主题配置、路由动画、多页面缓存、颗粒化按钮级别的权限控制、代码规范等功能，让你不操心技术，而专注于业务开发。

项目使用的技术较新，但同时包含各个方面的最佳实践，所以此项目可作为模板为企业赋能，也可作为学习素材为个人赋能，欢迎使用。

## 特性

- [React Hooks](https://react.dev/)
- [React Router](https://reactrouter.com/)
- [antd](https://ant.design/index-cn/)
- [Zustand](https://zustand-demo.pmnd.rs/)
- [Vite](https://vitejs.dev/)
- [I18n](https://react.i18next.com/)
- [TypeScript](https://www.typescriptlang.org/)
- [@tanstack/react-query](https://tanstack.com/query/latest/docs/framework/react/overview)
- [ESLint Flat Config](https://eslint.org/docs/latest/use/configure/configuration-files-new/)
- [KeepAlive](https://github.com/irychen/keepalive-for-react)
- [Mock Data](https://github.com/condorheroblog/vite-plugin-fake-server)

## 浏览器支持

| [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt=" Edge" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)<br>Edge | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/firefox/firefox_48x48.png" alt="Firefox" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)<br>Firefox | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/chrome/chrome_48x48.png" alt="Chrome" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)<br>Chrome | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/safari/safari_48x48.png" alt="Safari" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)<br>Safari |
|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| last 2 versions                                                                                                                                                                                       | last 2 versions                                                                                                                                                                                                  | last 2 versions                                                                                                                                                                                              | last 2 versions                                                                                                                                                                                              |
