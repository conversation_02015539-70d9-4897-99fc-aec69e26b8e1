import { rootApiConnector } from '~/connectors';
import { IPageRequest, IPageResponse } from '../@common';
import { CustomerAddressItemDto, ListCustomerAddressDto } from './types';

export const ENDPOINT_CUSTOMER_ADDRESS = {
  LIST: 'api/client/customer-address/list',
  CREATE: 'api/client/customer-address/create',
  SET_ACTIVE: 'api/client/customer-address/set-active',
  UPDATE: 'api/client/customer-address/update',
  SET_DEFAULT: 'api/client/customer-address/set-default',
};
class CustomerAddressApi {
  list(params: ListCustomerAddressDto) {
    return rootApiConnector.get<IPageResponse<CustomerAddressItemDto>>(ENDPOINT_CUSTOMER_ADDRESS.LIST, params);
  }

  create(data: CustomerAddressItemDto) {
    return rootApiConnector.post(ENDPOINT_CUSTOMER_ADDRESS.CREATE, data);
  }

  setActive(data: { id: string }) {
    return rootApiConnector.post(ENDPOINT_CUSTOMER_ADDRESS.SET_ACTIVE, data);
  }

  update(data: CustomerAddressItemDto) {
    return rootApiConnector.post(ENDPOINT_CUSTOMER_ADDRESS.UPDATE, data);
  }

  setDefault(data: { id: string }) {
    return rootApiConnector.post(ENDPOINT_CUSTOMER_ADDRESS.SET_DEFAULT, data);
  }
}
export const customerAddressApi = new CustomerAddressApi();
