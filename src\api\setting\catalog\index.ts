import { rootApiConnector } from '~/connectors';
import { IPageResponse } from '../../@common';
import {
  ICatalog,
  IListCatalog,
  IUpdateCatalog,
  ICreateCatalog,
} from './types';

const ENDPOINT = {
  LIST: 'api/client/catalog/list',
  CREATE: 'api/client/catalog/create',
  DETAIL: 'api/client/catalog/detail',
  UPDATE: 'api/client/catalog/update',
  ACTIVE: 'api/client/catalog/active',
  INACTIVE: 'api/client/catalog/inactive',
  LIST_ACTIVE: 'api/client/catalog/select-box',
};

class CatalogApi {
  list = (params: IListCatalog) => {
    return rootApiConnector.get<IPageResponse<ICatalog>>(ENDPOINT.LIST, params);
  };

  create = (body: ICreateCatalog) => {
    return rootApiConnector.post<ICatalog>(ENDPOINT.CREATE, body);
  };

  detail = (params: { id: string }) => {
    return rootApiConnector.get<ICatalog>(ENDPOINT.DETAIL, params);
  };

  update = (params: IUpdateCatalog) => {
    return rootApiConnector.post<IUpdateCatalog>(ENDPOINT.UPDATE, params);
  };

  active = (params: { id: string }) => {
    return rootApiConnector.post<ICatalog>(ENDPOINT.ACTIVE, params);
  };

  inactive = (params: { id: string }) => {
    return rootApiConnector.post<ICatalog>(ENDPOINT.INACTIVE, params);
  };

  listActive = (params: IListCatalog) => {
    return rootApiConnector.get<IPageResponse<ICatalog>>(ENDPOINT.LIST_ACTIVE, params);
  };
}
export const catalogApi = new CatalogApi();
