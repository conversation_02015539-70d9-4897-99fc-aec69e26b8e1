import { rootApiConnector } from '~/connectors';
import { ExampleItemDto } from './types';
import { IPageRequest, IPageResponse } from '../@common';

const ENDPOINT = {
  LIST: 'api/mock/example/list',
};
class ExampleApi {
  list(params: IPageRequest & { keyword?: string }) {
    return rootApiConnector.get<IPageResponse<ExampleItemDto>>(ENDPOINT.LIST, params);
  }
}
export const exampleApi = new ExampleApi();
