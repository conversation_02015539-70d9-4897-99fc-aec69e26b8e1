# Stage 1 - Build với Bun
FROM node:22.12-alpine AS build-deps
WORKDIR /usr/src/app

# Cài Bun
RUN apk add --no-cache curl bash \
    && curl -fsSL https://bun.sh/install | bash \
    && mv /root/.bun/bin/bun /usr/local/bin/bun

# Copy package files trước để cache install
COPY package.json bun.lockb* ./

# Cài deps bằng Bun
RUN bun install --frozen-lockfile

# Copy toàn bộ source code
COPY . ./

ARG VITE_ROOT_API_BASE_URL="https://crm-api-dev.apetechs.co"
ARG VITE_BASE_HOME_PATH="/home"
ARG VITE_GLOB_APP_TITLE="CRM"


ENV VITE_ROOT_API_BASE_URL=$VITE_ROOT_API_BASE_URL \
    VITE_BASE_HOME_PATH=$VITE_BASE_HOME_PATH \
    VITE_GLOB_APP_TITLE=$VITE_GLOB_APP_TITLE 


# Build với Vite (qua script build)
RUN bun run build

# Stage 2 - Serve với Nginx
FROM nginx:1.23-alpine

# Copy static files từ stage build
COPY --from=build-deps /usr/src/app/build /usr/share/nginx/html

# Thay nginx.conf custom
RUN rm /etc/nginx/conf.d/default.conf
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
