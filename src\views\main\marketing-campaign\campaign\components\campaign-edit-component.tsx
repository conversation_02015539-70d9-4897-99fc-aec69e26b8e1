import { SaveOutlined } from '@ant-design/icons';
import { Button, Checkbox, Col, DatePicker, Form, Input, Popconfirm, Row, Select, Tabs } from 'antd';
import BaseCard from '~/components/base-card';
import { CampaignHistoryComponent } from './campaign-history-component';
import { CampaignReportComponent } from './campaign-report-component';

const { TextArea } = Input;
const { Option } = Select;

export const CampaignEditComponent = () => {
  const [form] = Form.useForm();

  const tabs = [
    {
      key: '1',
      label: 'Báo cáo chiến dịch',
      children: <CampaignReportComponent />,
    },
    {
      key: '2',
      label: 'L<PERSON>ch sử thay đổi',
      children: <CampaignHistoryComponent />,
    },
  ];

  return (
    <BaseCard
      title="Chỉnh sửa chiến dịch dịch vụ Logistic"
      extra={
        <Popconfirm title="Bạn chắc chắn muốn thực hiện?" okText="Đồng ý" cancelText="Hủy">
          <div style={{ fontSize: 12, color: '#666', marginTop: 24 }}>
            <div>
              <PERSON><PERSON><PERSON><PERSON> tạo bởi <b><PERSON><PERSON><PERSON><PERSON>n A</b> vào lúc <b>09/06/2025 08:40:50</b>
            </div>
            <div>
              Cập nhật lần cuối bởi <b>Nguyễn Văn A</b> vào lúc <b>09/06/2025 08:40:50</b>
            </div>
          </div>
        </Popconfirm>
      }
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          campaignCode: 'LGST-2025-001',
          campaignName: 'Chiến dịch giao hàng nhanh nội thành Hà Nội',
          description:
            'Chiến dịch nhằm thúc đẩy tốc độ giao hàng trong nội thành Hà Nội với đội xe tải nhỏ và đội ngũ giao nhận chuyên nghiệp, đảm bảo giao hàng trong vòng 2 giờ.',
          branch: '2001 | Công Ty TNHH Vận Tải Nhanh 24H',
          contentName: 'Gói giao hàng nội thành siêu tốc',
          groupName: 'Khách hàng doanh nghiệp tháng 06/2025',
          status: 'Đã gửi',
          sendImmediately: true,
          scheduleTime: null,
        }}
      >
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              label="Mã chiến dịch"
              name="campaignCode"
              rules={[{ required: true, message: 'Vui lòng nhập mã chiến dịch' }]}
            >
              <Input disabled />
            </Form.Item>
          </Col>
          <Col span={16}>
            <Form.Item
              label="Tên chiến dịch"
              name="campaignName"
              rules={[{ required: true, message: 'Vui lòng nhập tên chiến dịch' }]}
            >
              <Input />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item label="Mô tả" name="description" rules={[{ required: true, message: 'Vui lòng nhập mô tả' }]}>
          <TextArea rows={4} />
        </Form.Item>

        <Form.Item label="Chi nhánh triển khai" name="branch">
          <Select allowClear showSearch>
            <Option value="2001 | Công Ty TNHH Vận Tải Nhanh 24H">2001 | Công Ty TNHH Vận Tải Nhanh 24H</Option>
          </Select>
        </Form.Item>

        <Form.Item
          label="Tên nội dung"
          name="contentName"
          rules={[{ required: true, message: 'Vui lòng chọn tên nội dung' }]}
        >
          <Select showSearch>
            <Option value="Gói giao hàng nội thành siêu tốc">Gói giao hàng nội thành siêu tốc</Option>
          </Select>
        </Form.Item>

        <Form.Item
          label="Nhóm khách hàng"
          name="groupName"
          rules={[{ required: true, message: 'Vui lòng chọn nhóm khách hàng' }]}
        >
          <Select showSearch>
            <Option value="Khách hàng doanh nghiệp tháng 06/2025">Khách hàng doanh nghiệp tháng 06/2025</Option>
          </Select>
        </Form.Item>

        <Form.Item label="Trạng thái" name="status">
          <Input disabled />
        </Form.Item>

        <Form.Item name="sendImmediately" valuePropName="checked">
          <Checkbox>Gửi ngay</Checkbox>
        </Form.Item>

        <Form.Item label="Thời gian gửi dự kiến" name="scheduleTime">
          <DatePicker showTime format="DD/MM/YYYY hh:mm A" style={{ width: '100%' }} />
        </Form.Item>

        <Row gutter={16}>
          <Col span={24} style={{ textAlign: 'center' }}>
            <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
              Lưu thay đổi
            </Button>
          </Col>
        </Row>
      </Form>
      <Tabs items={tabs} defaultActiveKey="1" />
    </BaseCard>
  );
};
