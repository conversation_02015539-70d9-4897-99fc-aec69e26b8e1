import { Select } from 'antd';
import { useDepartmentSelect } from './hook/useDepartmentSelect';
import { useCallback, useState } from 'react';

const { Option } = Select;

interface DepartmentSelectProps {
  onChange: (value: string) => void;
  isDisable?: boolean;
  value?: string;
}

const DepartmentSelect = ({ onChange, isDisable = false, value }: DepartmentSelectProps) => {
  const [limit, setLimit] = useState(50);
  const STEP = 50;
  const { data, isLoading, page, setPage } = useDepartmentSelect();

  const bumpLimit = useCallback(() => {
    const next = limit + STEP;
    setLimit(next);
    setPage({
      ...page,
      pageIndex: 1,
      pageSize: next,
    });
  }, [limit, page, setPage]);

  const handlePopupScroll: React.UIEventHandler<HTMLDivElement> = e => {
    const target = e.currentTarget;
    const nearBottom = target.scrollTop + target.clientHeight >= target.scrollHeight - 4;

    if (nearBottom && !isLoading) {
      bumpLimit();
    }
  };

  const handleChange = (value: string) => {
    onChange(value);
  };

  return (
    <Select
      loading={isLoading}
      showSearch
      placeholder="Chọn phòng ban"
      onPopupScroll={handlePopupScroll}
      onChange={handleChange}
      disabled={isDisable}
      value={value}
    >
      {data.length > 0 && data.map((item: any) => (
        <Option key={item.id} value={item.id}>
          {item.name}
        </Option>
      ))}
    </Select>
  );
};

export default DepartmentSelect;
