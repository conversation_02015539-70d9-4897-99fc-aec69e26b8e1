import type { AuthType, ForgotPasswordReq, PasswordLoginReq, ResetPasswordReq } from '~/api/auth/types';
import { useAccessStore, useAccountStore, useTabsStore } from '~/store';

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { authApi } from '~/api/auth';

const initialState = {
  accessToken: '',
  refreshToken: '',
};

type AuthState = AuthType;

interface AuthAction {
  login: (loginPayload: PasswordLoginReq) => Promise<any>;
  forgotPassword: (forgotPasswordPayload: ForgotPasswordReq) => Promise<any>;
  resetPassword: (resetPasswordPayload: ResetPasswordReq) => Promise<any>;
  logout: () => Promise<void>;
  reset: () => void;
}

export const useAuthStore = create<AuthState & AuthAction>()(
  persist(
    (set, get) => ({
      ...initialState,

      login: async loginPayload => {
        const response = await authApi.login(loginPayload);
        return set({
          ...response,
        });
      },
      forgotPassword: async forgotPasswordPayload => {
        const response = await authApi.forgotPassword(forgotPasswordPayload);
        return set({
          ...response,
        });
      },
      resetPassword: async resetPasswordPayload => {
        const response = await authApi.resetPassword(resetPasswordPayload);
        return set({
          ...response,
        });
      },
      logout: async () => {
        /**
         * clear token and other information
         */
        get().reset();
      },

      reset: () => {
        /**
         * Clear token
         */
        set({
          ...initialState,
        });
        /**
         * Clear user information
         * @see {@link https://github.com/pmndrs/zustand?tab=readme-ov-file#read-from-state-in-actions | Read from state in actions}
         */
        useAccountStore.getState().reset();

        /**
         * Clear permission information
         * @see https://github.com/pmndrs/zustand?tab=readme-ov-file#readingwriting-state-and-reacting-to-changes-outside-of-components
         */
        useAccessStore.getState().reset();

        /**
         * Clear tabs
         */
        useTabsStore.getState().resetTabs();

        /**
         * Clear keepAlive cache
         * In the container-layout component, automatically refresh the keepAlive cache based on openTabs
         */
      },
    }),
    { name: 'access-token' },
  ),
);
