import { useCallback, useEffect, useState } from 'react';
import { ICatalogItem, ICreateCatalogItem, IUpdateCatalogItem, IListCatalogItem } from '~/api/setting/catalog-item/types';
import { catalogItemApi } from '~/api/setting/catalog-item';
import { formatDateCustom } from '~/common/helpers/helper';

export const useCatalogItem = () => {
  const [data, setData] = useState<ICatalogItem[]>([]);
  const [detailData, setDetailData] = useState<ICatalogItem | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState<IListCatalogItem>({
    pageIndex: 1,
    pageSize: 10,
  });

  const loadData = useCallback(() => {
    setIsLoading(true);

    const { createdDate, ...rest } = page;
    const newPage = {
      ...rest,
      createdFrom: createdDate ? formatDateCustom(createdDate[0], 'YYYY-MM-DD') : undefined,
      createdTo: createdDate ? formatDateCustom(createdDate[1], 'YYYY-MM-DD') : undefined,
    };

    catalogItemApi
      .list(newPage)
      .then(result => {
        setData(result.data);
        setTotal(result.total);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [page]);

  // detail
  const detail = useCallback((id: string) => {
    setIsLoading(true);
    catalogItemApi
      .detail({ id })
      .then(result => {
        setDetailData(result);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  const create = useCallback((data: ICreateCatalogItem) => {
    setIsLoading(true);
    catalogItemApi
      .create(data)
      .then(result => {
        loadData();
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  // Update
  const update = useCallback((data: IUpdateCatalogItem) => {
    setIsLoading(true);
    catalogItemApi
      .update(data)
      .then(result => {
        loadData();
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  // Active
  const active = useCallback((id: string) => {
    setIsLoading(true);
    catalogItemApi
      .active({ id })
      .then(result => {
        loadData();
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  // Inactive
  const inactive = useCallback((id: string) => {
    setIsLoading(true);
    catalogItemApi
      .inactive({ id })
      .then(result => {
        loadData();
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    data,
    detailData,
    setData,
    detail,
    isLoading,
    setIsLoading,
    total,
    setTotal,
    page,
    setPage,
    create,
    update,
    active,
    inactive,
  };
};
