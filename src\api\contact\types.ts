import { IPageRequest } from '../@common';

export interface IContact {
  name?: string;
  position?: string;
  isDecisionMaker?: boolean;
  email?: string;
  phone?: string;
  note?: string;
}

export interface IContactUpdate extends IContact {
  id: string;
}

export interface IContactResponse {
  data: IContact[];
  total: number;
}

export interface IContactList extends IContact, IPageRequest {
  customerId?: string;
}
