{"menu": {"home": "Home", "access": "Access Demo", "accessMode": "Access Mode", "pageControl": "Page Control", "buttonControl": "Button Control", "adminVisible": "Visible to Admin", "commonVisible": "Commonly Visible", "demo": "Demo Page", "about": "About", "nestMenus": "Nested Menus", "menu1": "Menu1", "menu1-1": "Menu1-1", "menu1-2": "Menu1-2", "menu2": "Menu2", "outside": "External Page", "embedded": "Embedded", "externalLink": "External Link", "antd": "Ant Design", "projectDocs": "Project Documentation", "reactDocs": "React Documentation", "exception": "Exception Page", "exception_403": "403", "exception_404": "404", "exception_500": "500", "exceptionUnknownComponent": "Unknown Component", "system": "System Management", "user": "User Management", "role": "Role Management", "menu": "Menu Management", "dept": "Department Management", "personalCenter": "Personal Center", "profile": "My Profile", "settings": "Settings", "account": "Account Management", "listAccount": "Account List", "applications": "Application Management", "catalog": "Catalog Management", "catalog_item": "Catalog Item Management"}, "table": {"action": "Action", "pagination": {"total": "Total {{total}} items ({{from}}-{{to}})"}}, "appLoading": "Loading resources...", "back": "Back", "backHome": "Back to Home", "warning": "Warning", "confirm": "Confirm", "delete": "Delete", "fail": "Failed", "success": "Success", "deleteSuccess": "Deleted successfully", "confirmDelete": "Are you sure you want to delete?", "view": "View", "edit": "Edit", "error": "Error", "index": "Index", "upload": "Upload", "noData": "No Data", "action": "Action", "add": "Add", "addSuccess": "Added successfully", "backToHome": "Back to Home", "batchDelete": "<PERSON><PERSON> Delete", "cancel": "Cancel", "save": "Save", "close": "Close", "check": "Check", "expandColumn": "Expand Column", "columnSetting": "<PERSON>umn <PERSON>", "config": "Configuration", "keywordSearch": "Please enter a keyword to search", "modify": "Modify", "modifySuccess": "Modified successfully", "pleaseCheckValue": "Please check if the entered value is valid", "refresh": "Refresh", "reset": "Reset", "search": "Search", "switch": "Switch", "tip": "Tip", "tipMessage": "Confirm to perform this action?", "trigger": "<PERSON><PERSON>", "update": "Update", "updateSuccess": "Updated successfully", "userCenter": "User Center", "yes": "Yes", "no": "No", "name": "Name", "id": "Id", "status": "Status", "enabled": "Enabled", "deactivated": "Deactivated", "activated": "Activated", "disabled": "Disabled", "createTime": "Creation Time", "updateTime": "Update Time", "remark": "Remark", "demoOnly": "Demo only, operations are not effective", "pagination": "Total {{total}} items", "expandAll": "Expand All", "collapseAll": "Collapse All", "checkAll": "Select All", "cancelAll": "Cancel All", "register": "Register"}