import { QuoteStatus } from '~/common/enums/quote.enum';

export interface CreateQuotationReq {
  id?: string;
  tenantId: any;
  createdDate?: string;
  updatedDate?: string;
  createdBy?: any;
  updatedBy?: any;
  companyAddress: string;
  companyTaxCode: string;
  companyContactPerson: string;
  companyPhone: string;
  companyEmail: string;
  quotationDate: string;
  customerId: string;
  customerAddress: string;
  customerTaxCode: string;
  contacts: string;
  customerPhone: string;
  deliveryDate: string;
  deliveryLocation: string;
  paymentMethod: string;
  validityDays: number;
  notes: string;
  quotationProducts: QuotationProduct[];
  totalAmount: number;
}

export interface QuotationProduct {
  id?: string;
  tenantId?: any;
  createdDate?: string;
  updatedDate?: string;
  createdBy?: any;
  updatedBy?: any;
  code?: string;
  name: string;
  type: string;
  description: any;
  unit: string;
  currency: string;
  unitPrice: number;
  images: any;
  attachments: any;
  status?: string;
  quantity: number;
  totalBeforeVat: number;
  totalAfterVat: number;
  vat?: string;
}

export interface IListQouteRes {
  data: IListQouteItem[];
  total: number;
}

export interface IListQouteItem {
  id: string;
  tenantId: any;
  createdDate: string;
  updatedDate: string;
  createdBy: any;
  updatedBy: any;
  companyAddress: string;
  companyTaxCode: string;
  companyContactPerson: string;
  companyPhone: string;
  companyEmail: string;
  quotationNumber: string;
  quotationDate: string;
  customerId: string;
  customerName: any;
  customerAddress: string;
  customerTaxCode: string;
  contacts: string;
  customerPhone: string;
  deliveryDate: string;
  deliveryLocation: string;
  paymentMethod: string;
  validityDays: number;
  notes: string;
  status: any;
}

export interface UpdateStatusQuotationReq {
  id: string;
  status: QuoteStatus;
}
