import { Col, Row, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import BaseTable from '~/components/base-table';
import BaseView from '~/components/base-view';

interface IComplaint {
  id: string;
  code: string;
  description: string;
  type: string;
  status: string;
  supervisor: string;
  assignedTo: string;
  startDate: string;
  endDate: string;
  address: string;
}

const dummyData: IComplaint[] = [
  {
    id: '1',
    code: 'KN001',
    description: '<PERSON><PERSON>ế<PERSON> nại về chất lượng sản phẩm',
    type: 'Sản phẩm',
    status: '<PERSON>ang xử lý',
    supervisor: '<PERSON><PERSON><PERSON><PERSON>ăn A',
    assignedTo: 'Trần Thị B',
    startDate: '2024-03-20T08:00:00',
    endDate: '2024-03-25T17:00:00',
    address: '123 Đường ABC, Quận 1, TP.HCM',
  },
  {
    id: '2',
    code: 'KN002',
    description: '<PERSON><PERSON><PERSON><PERSON> nại về dịch vụ hậu mãi',
    type: 'Dịch vụ',
    status: '<PERSON><PERSON><PERSON> thành',
    supervisor: '<PERSON><PERSON>ăn <PERSON>',
    assignedTo: '<PERSON>ạ<PERSON> Thị D',
    startDate: '2024-03-15T09:30:00',
    endDate: '2024-03-18T16:00:00',
    address: '456 Đường XYZ, Quận 2, TP.HCM',
  },
  {
    id: '3',
    code: 'KN003',
    description: 'Khiếu nại về thời gian giao hàng',
    type: 'Vận chuyển',
    status: 'Đang xử lý',
    supervisor: 'Hoàng Văn E',
    assignedTo: 'Nguyễn Thị F',
    startDate: '2024-03-19T10:15:00',
    endDate: '2024-03-22T17:00:00',
    address: '789 Đường DEF, Quận 3, TP.HCM',
  },
  {
    id: '4',
    code: 'KN004',
    description: 'Khiếu nại về giá sản phẩm',
    type: 'Giá cả',
    status: 'Chờ xử lý',
    supervisor: 'Trần Văn G',
    assignedTo: 'Lê Thị H',
    startDate: '2024-03-21T14:20:00',
    endDate: '2024-03-24T17:00:00',
    address: '321 Đường GHI, Quận 4, TP.HCM',
  },
  {
    id: '5',
    code: 'KN005',
    description: 'Khiếu nại về thái độ nhân viên',
    type: 'Dịch vụ',
    status: 'Đang xử lý',
    supervisor: 'Phạm Văn I',
    assignedTo: 'Hoàng Thị K',
    startDate: '2024-03-18T16:45:00',
    endDate: '2024-03-21T17:00:00',
    address: '654 Đường JKL, Quận 5, TP.HCM',
  },
];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'Đang xử lý':
      return 'processing';
    case 'Hoàn thành':
      return 'success';
    case 'Chờ xử lý':
      return 'warning';
    default:
      return 'default';
  }
};

export const ComplaintTab = () => {
  const columns: ColumnsType<IComplaint> = [
    {
      title: 'STT',
      key: 'stt',
      width: 60,
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Mã khiếu nại',
      dataIndex: 'code',
      key: 'code',
      width: 100,
    },
    {
      title: 'Loại khiếu nại',
      dataIndex: 'type',
      key: 'type',
      width: 120,
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (value: string) => (
        <Tag color={getStatusColor(value)} style={{ fontSize: '14px', padding: '4px 12px' }}>
          {value}
        </Tag>
      ),
    },
    {
      title: 'Nhân viên theo dõi/ giám sát',
      dataIndex: 'supervisor',
      key: 'supervisor',
      width: 150,
    },
    {
      title: 'Nhân viên xử lý',
      dataIndex: 'assignedTo',
      key: 'assignedTo',
      width: 150,
    },
    {
      title: 'Ngày bắt đầu',
      dataIndex: 'startDate',
      key: 'startDate',
      width: 150,
      render: (value: string) => (value ? new Date(value).toLocaleDateString('vi-VN') : '-'),
    },
    {
      title: 'Ngày kết thúc',
      dataIndex: 'endDate',
      key: 'endDate',
      width: 150,
      render: (value: string) => (value ? new Date(value).toLocaleDateString('vi-VN') : '-'),
    },
    {
      title: 'Địa chỉ XLKN',
      dataIndex: 'address',
      key: 'address',
      width: 250,
    },
  ];

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={dummyData}
            total={dummyData.length}
            isLoading={false}
            scroll={{ x: 2000 }}
          />
        </Col>
      </Row>
    </BaseView>
  );
};
