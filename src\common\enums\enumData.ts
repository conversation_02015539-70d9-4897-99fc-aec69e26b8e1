export const enumData = {
  FILTER_TYPE: {
    INPUT: { key: 'input', description: 'String Input' },
    INPUT_NUMBER: { key: 'inputNumber', description: 'Number Input' },
    SELECT: { key: 'select', description: 'Select Input' },
    SELECT_SEARCH: { key: 'selectSearch', description: 'Select Input Allow Search' },
    DATE: { key: 'date', description: 'DatePicker' },
    DATE_RANGE: { key: 'dateRange', description: 'Date Picker Range' },
  },
  FILTER_QUOTE: {
    INPUT: { key: 'input', description: 'Tên khách hàng' },
    SELECT: { key: 'select', description: 'Trạng thái' },
  },
};
