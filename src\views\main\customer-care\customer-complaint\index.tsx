import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Checkbox, Col, Row, Tag, message } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { FC, useState } from 'react';
import { IComplaint, IFilterComplaint } from '~/api/customer-care/complaint/types';
import BaseButton from '~/components/base-button';
import BaseCard from '~/components/base-card';
import BaseTable from '~/components/base-table';
import BaseView from '~/components/base-view';
import CreateComplaintModal from './components/create-complaint-modal';
import DetailButton from './components/detail-button';
import EditButton from './components/edit-button';
import FilterProduct from './components/filter-product';

interface IProps {}

// Dữ liệu mẫu cho danh sách khiếu nại
const mockComplaints: IComplaint[] = [];

export const ComplaintView: FC<IProps> = (props: IProps) => {
  const [filter, setFilter] = useState<IFilterComplaint>({
    title: '',
    customerCode: '',
    sapCode: '',
    supervisorId: '',
    assignedStaffId: '',
    startDate: '',
    endDate: '',
    pageIndex: 1,
    pageSize: 10,
  });

  const [complaints, setComplaints] = useState<IComplaint[]>(mockComplaints);
  const [isLoading, setIsLoading] = useState(false);
  const [total, setTotal] = useState(mockComplaints.length);
  const [selectedRows, setSelectedRows] = useState<string[]>([]);
  const [visibleCreateModal, setVisibleCreateModal] = useState(false);

  const handleFilter = (values: IFilterComplaint) => {
    setIsLoading(true);

    // Giả lập thời gian xử lý
    setTimeout(() => {
      let filteredData = [...mockComplaints];

      // Lọc theo tiêu đề
      if (values.title) {
        filteredData = filteredData.filter(item => item.title.toLowerCase().includes(values.title.toLowerCase()));
      }

      // Lọc theo mã khách hàng
      if (values.customerCode) {
        filteredData = filteredData.filter(item =>
          item.customerCode.toLowerCase().includes(values.customerCode.toLowerCase()),
        );
      }

      // Lọc theo mã SAP
      if (values.sapCode) {
        filteredData = filteredData.filter(item => item.sapCode.toLowerCase().includes(values.sapCode.toLowerCase()));
      }

      // Lọc theo nhân viên giám sát
      if (values.supervisorId) {
        filteredData = filteredData.filter(item => item.supervisorId === values.supervisorId);
      }

      // Lọc theo nhân viên được phân công
      if (values.assignedStaffId) {
        filteredData = filteredData.filter(item => item.assignedStaffId === values.assignedStaffId);
      }

      // Lọc theo ngày bắt đầu
      if (values.startDate) {
        filteredData = filteredData.filter(item => new Date(item.startDate) >= new Date(values.startDate));
      }

      // Lọc theo ngày kết thúc
      if (values.endDate) {
        filteredData = filteredData.filter(item => item.endDate && new Date(item.endDate) <= new Date(values.endDate));
      }

      setComplaints(filteredData);
      setTotal(filteredData.length);
      setIsLoading(false);
    }, 500);
  };

  const handleReset = () => {
    setFilter({
      title: '',
      customerCode: '',
      sapCode: '',
      supervisorId: '',
      assignedStaffId: '',
      startDate: '',
      endDate: '',
      pageIndex: 1,
      pageSize: 10,
    });
    setComplaints(mockComplaints);
    setTotal(mockComplaints.length);
  };

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({
      ...filter,
      pageIndex: newPageIndex,
      pageSize: newPageSize,
    });
  };

  const handleDelete = async (item: IComplaint) => {
    try {
      setIsLoading(true);

      // Giả lập thời gian xử lý
      setTimeout(() => {
        const updatedComplaints = complaints.filter(complaint => complaint.id !== item.id);
        setComplaints(updatedComplaints);
        setTotal(updatedComplaints.length);
        setIsLoading(false);
        message.success('Xóa khiếu nại thành công');
      }, 500);
    } catch (error) {
      message.error('Xóa khiếu nại thất bại');
      setIsLoading(false);
    }
  };

  const columns: ColumnsType<IComplaint> = [
    {
      title: 'STT',
      dataIndex: 'index',
      key: 'index',
      width: 60,
      align: 'center',
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Chọn',
      dataIndex: 'select',
      key: 'select',
      width: 100,
      render: (_, record) => (
        <Checkbox
          checked={selectedRows.includes(record.id)}
          onChange={e => {
            if (e.target.checked) {
              setSelectedRows([...selectedRows, record.id]);
            } else {
              setSelectedRows(selectedRows.filter(id => id !== record.id));
            }
          }}
        />
      ),
    },
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 50,
      align: 'center',
    },
    {
      title: 'Tiêu đề',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      align: 'center',
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description',
      width: 200,
      align: 'center',
    },
    {
      title: 'Loại',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      align: 'center',
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (value: string) => (
        <Tag color="blue" style={{ fontSize: '14px', padding: '4px 12px' }}>
          {value?.toUpperCase() || 'UNKNOWN'}
        </Tag>
      ),
    },
    {
      title: 'Địa chỉ XLKN',
      dataIndex: 'complaintAddress',
      key: 'complaintAddress',
      width: 150,
      align: 'center',
    },
    {
      title: 'Mã khách hàng',
      dataIndex: 'customerCode',
      key: 'customerCode',
      width: 120,
      align: 'center',
    },
    {
      title: 'Mã SAP',
      dataIndex: 'sapCode',
      key: 'sapCode',
      width: 120,
      align: 'center',
    },
    {
      title: 'Khách hàng',
      dataIndex: 'customer',
      key: 'customer',
      width: 150,
      align: 'center',
    },
    {
      title: 'Địa chỉ',
      dataIndex: 'address',
      key: 'address',
      width: 200,
      align: 'center',
    },
    {
      title: 'NV theo dõi/giám sát',
      dataIndex: 'supervisor',
      key: 'supervisor',
      width: 150,
      align: 'center',
    },
    {
      title: 'NV được phân công',
      dataIndex: 'assignedStaff',
      key: 'assignedStaff',
      width: 150,
      align: 'center',
    },
    {
      title: 'Mức độ',
      dataIndex: 'priority',
      key: 'priority',
      width: 100,
      align: 'center',
    },
    {
      title: 'Ngày bắt đầu',
      dataIndex: 'startDate',
      key: 'startDate',
      width: 120,
      align: 'center',
    },
    {
      title: 'Ngày hết hạn',
      dataIndex: 'dueDate',
      key: 'dueDate',
      width: 120,
      align: 'center',
    },
    {
      title: 'Ngày kết thúc',
      dataIndex: 'endDate',
      key: 'endDate',
      width: 120,
      align: 'center',
    },
    {
      title: 'Thời gian checkin',
      dataIndex: 'checkinTime',
      key: 'checkinTime',
      width: 120,
      align: 'center',
    },
    {
      title: 'Thời gian checkout',
      dataIndex: 'checkoutTime',
      key: 'checkoutTime',
      width: 120,
      align: 'center',
    },
    {
      title: 'Trạng thái gửi tin',
      dataIndex: 'notificationStatus',
      key: 'notificationStatus',
      width: 120,
    },
    {
      title: 'Chức năng',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record: any) => (
        <>
          <DetailButton data={record} />
          <EditButton data={record} />
          <BaseButton
            danger
            type="primary"
            shape="circle"
            icon={<DeleteOutlined />}
            tooltip="Delete"
            onClick={() => handleDelete(record)}
          />
        </>
      ),
    },
  ];

  return (
    <BaseCard
      title="Danh sách xử lý khiếu nại"
      buttons={[
        {
          text: 'Thêm mới khiếu nại',
          icon: <PlusOutlined />,
          onClick: () => {
            setVisibleCreateModal(true);
          },
          type: 'primary',
        },
      ]}
    >
      {visibleCreateModal && (
        <CreateComplaintModal
          open={visibleCreateModal}
          onClose={() => setVisibleCreateModal(false)}
          onSuccess={() => setVisibleCreateModal(false)}
        />
      )}
      <BaseView>
        <Row gutter={16}>
          <Col span={24}>
            <FilterProduct onFilter={handleFilter} onReset={handleReset} isLoading={isLoading} />
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24} style={{ marginTop: 16 }}>
            <BaseTable
              columns={columns as any}
              data={complaints}
              total={total}
              isLoading={isLoading}
              onPageChange={handlePageChange}
              scroll={{ x: 3000 }}
            />
          </Col>
        </Row>
      </BaseView>
    </BaseCard>
  );
};
