import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Col, Collapse, DatePicker, Form, Input, Row, Select } from 'antd';
import { FC, useCallback } from 'react';
import { IFilterCustomerContact } from '~/api/customer-care/complaint/types';

interface IProps {
  onFilter: (values: IFilterCustomerContact) => void;
  onReset: () => void;
  isLoading: boolean;
  customerData: any;
}

const FilterProduct: FC<IProps> = (props: IProps) => {
  const { onFilter, onReset, isLoading } = props;
  const [form] = Form.useForm();

  const handleFilter = useCallback(() => {
    onFilter(form.getFieldsValue());
  }, [form, onFilter]);

  const handleReset = useCallback(() => {
    form.resetFields();
    onReset();
  }, [form, onReset]);

  return (
    <Collapse>
      <Collapse.Panel header="<PERSON><PERSON><PERSON> kiếm" key="0">
        <Form form={form} layout="vertical">
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item name="id" label="Mã khách hàng">
                <Input placeholder="Nhập mã khách hàng" />
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item name="name" label="Tên khách hàng">
                <Select placeholder="Nhập tên khách hàng">
                  {props.customerData.map(c => (
                    <Select.Option value={c.id} key={c.id}>
                      {c.customerCode}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item name="title" label="Tiêu đề">
                <Input placeholder="Nhập tiêu đề" />
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item name="status" label="Trạng thái">
                <Select placeholder="Chọn trạng thái">
                  <Select.Option value="PENDING">Đang chờ</Select.Option>
                  <Select.Option value="PROCESSING">Đang xử lý</Select.Option>
                </Select>
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item name="observerId" label="NV theo dõi/giám sát">
                <Input placeholder="Chọn NV theo dõi/giám sát" />
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item name="expectedDate" label="Ngày dự kiến">
                <DatePicker placeholder="Chọn ngày dự kiến" allowClear style={{ width: '100%' }} />
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item name="fromDateToDate" label="Từ ngày - đến ngày">
                <DatePicker.RangePicker style={{ width: '100%' }} allowClear placeholder={['Từ ngày', 'Đến ngày']} />
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item name="statusSend" label="Trạng thái gửi tin">
                <Select placeholder="Chọn trạng thái gửi tin">
                  <Select.Option value="SEEN">Đã xem</Select.Option>
                  <Select.Option value="UNSEEN">Chưa xem</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24} style={{ marginTop: 10 }}>
            <Form.Item style={{ width: '100%' }}>
              <div
                style={{
                  display: 'flex',
                  gap: 10,
                  justifyContent: 'center',
                }}
              >
                <Button
                  type="primary"
                  style={{ width: '15%' }}
                  htmlType="submit"
                  onClick={handleFilter}
                  loading={isLoading}
                >
                  <SearchOutlined />
                  Tìm kiếm
                </Button>
                <Button type="default" style={{ width: '15%' }} htmlType="submit" onClick={handleReset}>
                  <ReloadOutlined />
                  Làm mới
                </Button>
              </div>
            </Form.Item>
          </Row>
        </Form>
      </Collapse.Panel>
    </Collapse>
  );
};

export default FilterProduct;
