import { useTranslation } from 'react-i18next';

export const useToast = () => {
  const { t } = useTranslation();
  const success = (message: string) => {
    window.$message?.success(t(message));
    setTimeout(() => {
      window.$message?.destroy();
    }, 1000);
  };

  const error = (message: string) => {
    window.$message?.error(t(message));
    setTimeout(() => {
      window.$message?.destroy();
    }, 1000);
  };

  const warning = (message: string) => {
    window.$message?.warning(t(message));
    setTimeout(() => {
      window.$message?.destroy();
    }, 1000);
  };

  const info = (message: string) => {
    window.$message?.info(t(message));
    setTimeout(() => {
      window.$message?.destroy();
    }, 1000);
  };

  const handleError = (err?: any) => {
    const message = err?.data?.message ?? err?.message ?? (err?.reason ? err?.reason : 'Unknown');
    const translateMessage = t(`error.${message}`);
    window.$message?.error(translateMessage);
    setTimeout(() => {
      window.$message?.destroy();
    }, 1000);
  };

  const destroy = () => {
    window.$message?.destroy();
  };

  return {
    success,
    error,
    warning,
    info,
    destroy,
    handleError,
  };
};
