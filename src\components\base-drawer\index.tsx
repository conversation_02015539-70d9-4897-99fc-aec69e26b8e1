import type { DrawerProps } from 'antd';
import { But<PERSON>, Drawer, Space } from 'antd';
import React from 'react';

interface ButtonConfig {
  text: string;
  icon?: React.ReactNode;
  onClick?: () => void;
  type?: 'primary' | 'default' | 'dashed' | 'link' | 'text';
  danger?: boolean;
}

interface BaseDrawerProps extends Omit<DrawerProps, 'title' | 'extra' | 'children' | 'open' | 'onClose'> {
  open: boolean;
  onClose: () => void;
  title?: React.ReactNode;
  buttons?: ButtonConfig[];
  hidden?: boolean;
  children?: React.ReactNode;
  extra?: React.ReactNode;
  buttonText?: string;
  onButtonClick?: () => void;
}

const BaseDrawer: React.FC<BaseDrawerProps> = ({
  open,
  onClose,
  title,
  buttons = [],
  buttonText,
  onButtonClick,
  hidden = false,
  children,
  extra,
  ...rest
}) => {
  // Hợp nhất danh sách nút từ cách mới và cách cũ (nếu có)
  const allButtons = [...buttons];

  return (
    <Drawer
      open={open}
      onClose={onClose}
      title={title}
      className="custom-drawer"
      extra={
        !hidden && (
          <Space>
            {extra}
            {allButtons.map((btn, index) => (
              <Button
                className=""
                key={index}
                type={btn.type || 'default'}
                icon={btn.icon}
                onClick={btn.onClick}
                danger={btn.danger}
              >
                {btn.text}
              </Button>
            ))}
          </Space>
        )
      }
      {...rest}
    >
      <div className="custom-drawer-content">{children}</div>
    </Drawer>
  );
};

export default BaseDrawer;
