import { PlayCircleOutlined, PlusOutlined, StopOutlined } from '@ant-design/icons';
import { Button, Col, notification, Row, Tag, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { ECustomer } from '~/common/enums/customer.enum';

import BaseConfirmButton from '~/components/base-confirm-button';
import BaseFilter from '~/components/base-filter/BaseFilter';
import BaseTable from '~/components/base-table';
import BaseText from '~/components/base-text';

import DetailButton from './component/detail-button';
import EditButton from './component/edit-button';

import { ICustomer } from '~/api/customer/types';
import { IEmployee } from '~/api/setting/employee/types';
import BaseCard from '~/components/base-card';
import { useEmployee } from '../../setting/employee/hook/useEmployee';
import useCustomer from '../Hooks/useCustomer';
import useFilter from '../Hooks/useFilter';
import CreateCustomerModal from './component/create-customer-modal';

const ListCustomerView = () => {
  const { t } = useTranslation();

  // Mock data thay thế cho API
  const [data, setData] = useState<ICustomer[]>([]);

  const [total, setTotal] = useState<number>(2);

  // Mock data nhân viên
  const [employees, setEmployees] = useState<IEmployee[]>([]);

  const { data: employeesData, setPage } = useEmployee();

  const { filterData, setFilterData, filterFields, setSelectedStage, selectedStage, handleFilter, handleFilterReset } =
    useFilter(employees);

  const { listCustomer, setActiveCustomer } = useCustomer();

  const loadCustomer = (params: any) => {
    listCustomer(params).then(res => {
      setData(res.data);
      setTotal(res.total);
    });
  };
  useEffect(() => {
    setPage({
      pageIndex: 1,
      pageSize: 20,
    });
    loadCustomer(filterData);
  }, [filterData]);

  useEffect(() => {
    setEmployees(employeesData);
  }, [employeesData]);

  const [visibleCreateModal, setVisibleCreateModal] = useState<boolean>(false);

  const handleClickFilter = (stage: string, data: any) => {
    if (stage === selectedStage) {
      setSelectedStage(null);
      const newFilterData = { ...filterData };
      delete newFilterData.customerType;
      setFilterData(newFilterData);
      return;
    }
    setSelectedStage(stage);
    setFilterData({
      ...filterData,
      customerType: stage,
    });
  };

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilterData({
      ...filterData,
      pageIndex: newPageIndex,
      pageSize: newPageSize,
    });
  };

  const handleDelete = (record: ICustomer) => {
    setActiveCustomer(record.id)
      .then((res: any) => {
        if (res) {
          listCustomer(filterData).then(res => {
            setData(res.data);
            setTotal(res.total);
          });
          notification.success({
            message: res.message,
            placement: 'top',
          });
        }
      })
      .catch(err => {
        notification.error({
          message: err.message,
          placement: 'top',
        });
      });
  };

  const columns: ColumnsType<ICustomer> = [
    {
      title: t('customer.customer.columns.stt'),
      key: 'stt',
      width: 50,
      align: 'center',
      render: (_, __, index) => index + 1,
    },
    {
      title: t('customer.customer.columns.code') || 'Mã khách hàng',
      dataIndex: 'code',
      key: 'code',
      width: 200,
      align: 'center',
      render: (value: string, record: any, index: number) => {
        return <BaseText>{value || '--'}</BaseText>;
      },
    },
    {
      title: t('customer.customer.columns.name') || 'Tên khách hàng',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      align: 'center',
      render: (value: string, record: any, index: number) => {
        return <BaseText>{value || '--'}</BaseText>;
      },
    },
    {
      title: t('customer.customer.columns.phone') || 'Số điện thoại',
      dataIndex: 'phone',
      key: 'phone',
      width: 100,
      align: 'center',
      render: (value: string, record: any, index: number) => {
        return <BaseText>{value || '--'}</BaseText>;
      },
    },
    {
      title: t('customer.customer.columns.salesRep') || 'Nhân viên phụ trách',
      dataIndex: 'salesRep',
      key: 'salesRep',
      width: 150,
      align: 'center',
      render: (value: any, record: any, index: number) => {
        const salesRep = employees.find(item => item.id === value);
        return <BaseText>{salesRep?.fullName || '--'}</BaseText>;
      },
    },
    {
      title: t('customer.customer.columns.address') || 'Địa chỉ',
      dataIndex: 'address',
      key: 'address',
      width: 300,
      align: 'center',
      render: (value: string, record: any, index: number) => {
        return <BaseText>{value || '--'}</BaseText>;
      },
    },
    {
      title: t('customer.customer.columns.source') || 'Nguồn khách hàng',
      dataIndex: 'source',
      key: 'source',
      width: 100,
      align: 'center',
      render: (value: string, record: any, index: number) => {
        return <BaseText>{value || '--'}</BaseText>;
      },
    },
    {
      title: t('customer.customer.columns.isActive') || 'Trạng thái hoạt động',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 100,
      align: 'center',
      render: (value: boolean) => {
        if (value === null) return <BaseText>--</BaseText>;
        return <Tag color={value ? 'green' : 'red'}>{value ? 'Hoạt động' : 'Không hoạt động'}</Tag>;
      },
    },
    {
      title: t('customer.customer.columns.createdDate') || 'Ngày tạo',
      dataIndex: 'createdDate',
      key: 'createdDate',
      width: 100,
      align: 'center',
      render: (value: string) => (value ? new Date(value).toLocaleDateString('vi-VN') : '-'),
    },
    {
      title: t('customer.customer.columns.createdBy') || 'Người tạo',
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 100,
      align: 'center',
      render: (value: string) => (value ? <BaseText>{value || '--'}</BaseText> : <BaseText>--</BaseText>),
    },
    {
      title: t('customer.customer.columns.action') || 'Tác vụ',
      key: 'action',
      width: 100,
      fixed: 'right',
      align: 'center',
      render: (value: any, record: ICustomer, index: number) => {
        return (
          <div style={{ display: 'flex', gap: 5, justifyContent: 'center' }}>
            <EditButton data={record} onClose={() => loadCustomer(filterData)} />
            <DetailButton data={record} />
            <BaseConfirmButton
              danger={record.isActive}
              tooltip={record.isActive ? 'Ngưng hoạt động' : 'Hoạt động lại'}
              type="default"
              shape="circle"
              icon={record.isActive ? <StopOutlined /> : <PlayCircleOutlined />}
              onConfirm={() => handleDelete(record)}
              confirmTitle={record.isActive ? 'Ngưng hoạt động' : 'Hoạt động lại'}
              size="middle"
            />
          </div>
        );
      },
    },
  ];

  return (
    <BaseCard
      title="Quản lý khách hàng"
      buttons={[
        {
          text: 'Thêm mới khách hàng',
          icon: <PlusOutlined />,
          onClick: () => {
            setVisibleCreateModal(true);
          },
          type: 'primary',
        },
      ]}
    >
      {visibleCreateModal && (
        <CreateCustomerModal
          open={visibleCreateModal}
          onClose={() => {
            setVisibleCreateModal(false);
            loadCustomer(filterData);
          }}
          onSuccess={() => {
            setVisibleCreateModal(false);
            loadCustomer(filterData);
          }}
        />
      )}
      <BaseFilter onReset={handleFilterReset} isLoading={false} filters={filterFields} onFilter={handleFilter} />
      <Row
        style={{
          marginTop: 16,
          marginBottom: 16,
          gap: 14,
          justifyContent: 'flex-start',
          width: '98%',
        }}
      >
        {Object.values(ECustomer.ECustomerType).map(({ name, value, textColor, label, color, description }) => (
          <Tooltip key={value} title={description}>
            <Button
              type={selectedStage === value ? 'default' : 'primary'}
              onClick={() => handleClickFilter(value, null)}
              style={{
                borderRadius: 6,
                padding: 10,
                height: 'auto',
                fontSize: '14px',
                fontWeight: selectedStage === value ? '500' : '600',
                border: `1.5px solid ${color}`,
                backgroundColor: selectedStage === value ? `${color}20` : color,
                color: selectedStage === value ? '#000000ff' : textColor,
                // opacity: selectedStage === value ?  1,
                transition: 'all 0.3s ease',
                boxShadow: selectedStage === value ? `-3px 3px 3px ${color} 40` : `-3px 3px 3px ${color}20`,
              }}
              onMouseEnter={e => {
                if (selectedStage !== value) {
                  e.currentTarget.style.backgroundColor = `${color}35`;
                  e.currentTarget.style.color = '#000000ff';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = `0 4px 12px ${color}35`;
                  e.currentTarget.style.fontWeight = '600';
                }
              }}
              onMouseLeave={e => {
                if (selectedStage !== value) {
                  e.currentTarget.style.backgroundColor = `${color}`;
                  e.currentTarget.style.color = textColor;
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = `0 2px 6px ${color}20`;
                  e.currentTarget.style.fontWeight = '500';
                }
              }}
            >
              {name}
            </Button>
          </Tooltip>
        ))}
      </Row>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={data}
            total={total}
            isLoading={false}
            onPageChange={handlePageChange}
            scroll={{ x: '2000px' }}
          />
        </Col>
      </Row>
    </BaseCard>
  );
};

export default ListCustomerView;
