import { EditOutlined, SaveOutlined } from '@ant-design/icons';
import {
  <PERSON><PERSON>,
  Card,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  InputNumber,
  notification,
  Row,
  Select,
  Upload,
} from 'antd';
import { useForm } from 'antd/es/form/Form';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { useCallback, useEffect, useState } from 'react';
import { ICustomer } from '~/api/customer/types';
import { IQuotation } from '~/api/quotation/types';
import { formatMoneyVND } from '~/common/helpers/helper';
import BaseButton from '~/components/base-button';
import BaseModal from '~/components/base-modal';
import BaseTable from '~/components/base-table';
import useCustomer from '~/views/main/customer/Hooks/useCustomer';
import useCustomerContact from '~/views/main/customer/Hooks/useCustomerContact';
import { useCatalogItem } from '~/views/main/setting/catalog-item/hook/useCatalogItem';
import { useQuotation } from '../Hooks/useQuotation';

interface QuotationEditComponentProps {
  data: IQuotation;
  onSuccess?: () => void;
}

const QuotationEditComponent = ({ data, onSuccess }: QuotationEditComponentProps) => {
  const {
    data: catalogItem,
    total: totalCatalogItem,
    setPage: setPageCatalogItem,
    isLoading: isLoadingCatalogItem,
  } = useCatalogItem();
  const [customerData, setCustomerData] = useState<ICustomer[]>([]);
  const [customerContactData, setCustomerContactData] = useState<any[]>([]);

  const [open, setOpen] = useState(false);
  const [form] = useForm();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingCatalogs, setIsLoadingCatalogs] = useState(false);
  const [openAddProductModal, setOpenAddProductModal] = useState(false);
  const [quotationProducts, setQuotationProducts] = useState<any[]>(data.quotationProducts || []);
  const [editingRowIndex, setEditingRowIndex] = useState<number | null>(null);
  const [tempEditData, setTempEditData] = useState<any>(null);

  const { listCustomer } = useCustomer();
  const { updateQuotation } = useQuotation();
  const { listCustomerContact } = useCustomerContact();
  // Xử lý khi chọn khách hàng
  const handleCustomerChange = (value: string) => {
    const customer = customerData.find(c => c.id === value);
    listCustomerContact({ customerId: value }).then(res => {
      setCustomerContactData(res.data);
    });
    form.setFieldsValue({
      memberId: undefined,
      customerAddress: customer?.addressLine || '',
      customerTaxCode: customer?.taxNumber || '',
      customerContactPerson: undefined,
      customerPhone: customer?.phone,
    });
  };

  //Khởi tạo data
  const initData = async () => {
    setIsLoadingCatalogs(true);
    try {
      const [customer, customerContact] = await Promise.all([
        listCustomer({}),
        listCustomerContact({ customerId: data.customerId }),
      ]);
      setCustomerData(customer.data);
      setCustomerContactData(customerContact.data);

      setIsLoadingCatalogs(false);
    } catch (error) {
      setIsLoadingCatalogs(false);
    }
  };

  useEffect(() => {
    if (open) {
      initData();
    }
  }, [open]);
  // Mở modal
  const openModal = () => {
    setOpen(true);
    setTimeout(() => {
      form.setFieldsValue({
        ...data,
        quotationDate: dayjs(data.quotationDate),
        deliveryDate: dayjs(data.deliveryDate),
      });
    }, 100);
  };

  // Đóng modal
  const closeModal = () => {
    setOpen(false);
    //destroy
    form.resetFields();
    setQuotationProducts(data.quotationProducts || []);
  };

  // Tính tổng trị giá
  const calculateTotalValue = useCallback(() => {
    return quotationProducts.reduce((total: number, product: any) => {
      const totalAfterVat = product.totalAfterVat || 0;
      return total + (typeof totalAfterVat === 'string' ? parseFloat(totalAfterVat) : totalAfterVat);
    }, 0);
  }, [quotationProducts]);

  // Cập nhật tổng trị giá trong form mỗi khi quotationProducts thay đổi
  useEffect(() => {
    const totalAmount = calculateTotalValue();
    form.setFieldValue('totalAmount', totalAmount);
  }, [quotationProducts, form, calculateTotalValue]);

  const handleSubmit = async (values: any) => {
    if (!values) {
      return;
    }
    const updateData = { id: data.id, ...values, quotationProducts };
    delete updateData?.customer;
    setIsSubmitting(true);
    updateQuotation(updateData)
      .then(res => {
        if (res) {
          setIsSubmitting(false);
          closeModal();
          notification.success({
            message: res.message,
            placement: 'top',
          });
        }
      })
      .catch(err => {
        setIsSubmitting(false);
        notification.error({
          message: err.message,
          placement: 'top',
        });
      })
      .finally(() => {
        setIsSubmitting(false);
        onSuccess?.();
      });
  };

  // Hàm bắt đầu chỉnh sửa hàng
  const handleStartEdit = (record: any, index: number) => {
    setEditingRowIndex(index);
    setTempEditData({ ...record });
  };

  // Hàm lưu thay đổi
  const handleSaveEdit = async (index: number) => {
    setQuotationProducts(prev => {
      const newItems = [...prev];
      newItems[index] = tempEditData;
      return newItems;
    });
    setEditingRowIndex(null);
    setTempEditData(null);
  };

  // Hàm hủy chỉnh sửa
  const handleCancelEdit = () => {
    setEditingRowIndex(null);
    setTempEditData(null);
  };

  // Hàm cập nhật dữ liệu tạm thời
  const updateTempData = (field: string, value: any) => {
    if (tempEditData) {
      const updatedData = { ...tempEditData, [field]: value };

      // Tính toán lại các giá trị liên quan
      if (field === 'quantity' || field === 'unitPrice' || field === 'vat') {
        const quantity = field === 'quantity' ? value : updatedData.quantity || 1;
        const unitPrice = field === 'unitPrice' ? value : updatedData.unitPrice || 0;
        const vat = field === 'vat' ? value : updatedData.vat || 0;

        updatedData.totalBeforeVat = quantity * unitPrice;
        updatedData.totalAfterVat = Number(quantity * unitPrice * (1 + vat / 100)).toFixed(2);
      }

      setTempEditData(updatedData);
    }
  };

  // Xử lý xóa sản phẩm
  const handleDeleteItem = (id: string) => {
    const newItems = quotationProducts.filter(item => item.id !== id);
    setQuotationProducts(newItems);
  };

  // Xử lý thêm sản phẩm mới
  const handleAddItem = (catalog: any) => {
    const newItem = {
      catalogId: catalog.id,
      catalogItemId: catalog.id,
      code: catalog.code,
      name: catalog.name,
      unit: catalog.unit || 'Không có',
      quantity: 1,
      unitPrice: catalog.unitPrice,
      totalBeforeVat: catalog.unitPrice,
      vat: 10,
      totalAfterVat: Math.round(catalog.unitPrice * 1.1),
      type: catalog.type || 'Không có',
      description: catalog.description,
      currency: catalog.currency || 'Không có',
      images: catalog.images,
      attachments: catalog.attachments,
      status: catalog.status,
    };
    //check item trùng thì báo lỗi
    const isDuplicate = quotationProducts.find(item => item.catalogId === newItem.catalogId);
    if (isDuplicate) {
      notification.warning({
        message: 'Sản phẩm đã tồn tại',
        placement: 'top',
      });
      return;
    }
    const newItems = [...quotationProducts, newItem];
    setQuotationProducts(newItems);
    setOpenAddProductModal(false);
  };

  const columns: ColumnsType<any> = [
    {
      title: 'STT',
      dataIndex: 'stt',
      align: 'center' as const,
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Mã sản phẩm',
      dataIndex: 'code',
    },
    {
      title: 'Sản phẩm',
      dataIndex: 'name',
    },
    {
      title: 'Đơn vị',
      dataIndex: 'unit',
    },
    {
      title: 'Số lượng',
      dataIndex: 'quantity',
      render: (value, record, index) => {
        const isEditing = editingRowIndex === index;
        const displayValue = isEditing ? tempEditData?.quantity : value;

        return isEditing ? (
          <InputNumber
            min={1}
            value={displayValue || 1}
            formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.')}
            onChange={val => updateTempData('quantity', val || 1)}
          />
        ) : (
          <span>{displayValue || 1}</span>
        );
      },
    },
    {
      title: 'Đơn giá',
      dataIndex: 'unitPrice',
      render: (value, record, index) => {
        const isEditing = editingRowIndex === index;
        const displayValue = isEditing ? tempEditData?.unitPrice : value;

        return isEditing ? (
          <InputNumber
            min={0}
            value={displayValue || 0}
            style={{ width: '100%' }}
            formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.')}
            parser={value => (value ? value.replace(/\./g, '') : '')}
            onChange={val => updateTempData('unitPrice', val || 0)}
          />
        ) : (
          <span>{formatMoneyVND(displayValue || 0)}</span>
        );
      },
    },
    {
      title: 'Thành tiền chưa VAT',
      dataIndex: 'totalBeforeVat',
      render: (value, record, index) => {
        const isEditing = editingRowIndex === index;
        const displayValue = isEditing ? tempEditData?.totalBeforeVat : value;
        return formatMoneyVND(displayValue || 0);
      },
    },
    {
      title: 'VAT (%)',
      dataIndex: 'vat',
      render: (value, record, index) => {
        const isEditing = editingRowIndex === index;
        const displayValue = isEditing ? tempEditData?.vat : value;

        return isEditing ? (
          <InputNumber min={0} max={100} value={displayValue || 0} onChange={val => updateTempData('vat', val || 0)} />
        ) : (
          <span>{displayValue || 0}%</span>
        );
      },
    },
    {
      title: 'Thành tiền sau VAT',
      dataIndex: 'totalAfterVat',
      render: (value, record, index) => {
        const isEditing = editingRowIndex === index;
        const displayValue = isEditing ? tempEditData?.totalAfterVat : value;
        return formatMoneyVND(displayValue || 0);
      },
    },
    {
      title: 'Tác vụ',
      align: 'center' as const,
      fixed: 'right' as const,
      width: 120,
      render: (_, record, index) => {
        const isEditing = editingRowIndex === index;

        return (
          <div style={{ display: 'flex', gap: '8px' }}>
            {isEditing ? (
              <>
                <Button icon={<SaveOutlined />} onClick={() => handleSaveEdit(index)} loading={isSubmitting}>
                  Lưu
                </Button>
                <Button onClick={handleCancelEdit}>Hủy</Button>
              </>
            ) : (
              <>
                <Button type="default" icon={<EditOutlined />} onClick={() => handleStartEdit(record, index)}>
                  Sửa
                </Button>
                <Button danger loading={isSubmitting} onClick={() => handleDeleteItem(record.id)}>
                  Xóa
                </Button>
              </>
            )}
          </div>
        );
      },
    },
  ];

  const columnsAddProduct: ColumnsType<any> = [
    {
      title: 'STT',
      dataIndex: 'stt',
      key: 'stt',
      align: 'center',
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Mã sản phẩm',
      dataIndex: 'code',
      key: 'code',
      align: 'center',
    },
    {
      title: 'Sản phẩm',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: 'Đơn vị',
      dataIndex: 'unit',
      align: 'center',
    },

    {
      title: 'Tác vụ',
      render: (_, record) => {
        return (
          <Button type="primary" onClick={() => handleAddItem(record)}>
            Thêm
          </Button>
        );
      },
    },
  ];

  const modalContent = (
    <Form form={form} layout="vertical" onFinish={handleSubmit}>
      <Card title="Công ty">
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              label="Số báo giá"
              name="quotationNumber"
              rules={[{ required: true, message: 'Vui lòng nhập số báo giá' }]}
            >
              <Input placeholder="Nhập số báo giá" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="Ngày báo giá"
              name="quotationDate"
              required
              rules={[{ required: true, message: 'Vui lòng chọn ngày báo giá' }]}
            >
              <DatePicker placeholder="Ngày báo giá" format="YYYY-MM-DD" style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider />
      <Card title="Thông tin khách hàng">
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item label="Khách hàng" name="customerId" rules={[{ required: true }]}>
              <Select placeholder="Chọn khách hàng" onChange={handleCustomerChange} allowClear>
                {customerData.map(c => (
                  <Select.Option value={c.id}>{c.name}</Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="Địa chỉ" name="customerAddress">
              <Input placeholder="Địa chỉ khách hàng" disabled />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="Mã số thuế" name="customerTaxCode">
              <Input placeholder="Mã số thuế khách hàng" disabled />
            </Form.Item>
          </Col>

          <Col span={16}>
            <Form.Item
              label="Người liên hệ"
              name="memberId"
              required
              rules={[{ required: true, message: 'Vui lòng chọn người liên hệ' }]}
            >
              {customerData && (
                <Select
                  placeholder="Chọn người liên hệ"
                  // onChange={handleContactChange}
                  disabled={form.getFieldValue('customerId') === undefined}
                  allowClear
                >
                  {customerContactData.map(c => (
                    <Select.Option value={c.id} key={c.id}>
                      {c.name}
                    </Select.Option>
                  ))}
                </Select>
              )}
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="Điện thoại" name="customerPhone">
              <Input placeholder="Số điện thoại liên hệ" disabled />
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider />
      <Card
        title="Thông tin hàng hoá"
        extra={
          <Button
            type="primary"
            onClick={() => {
              setOpenAddProductModal(true);
            }}
          >
            Thêm
          </Button>
        }
      >
        <BaseTable
          columns={columns}
          data={quotationProducts}
          total={quotationProducts?.length}
          isLoading={isLoadingCatalogs}
          scroll={{ x: 'max-content' }}
        />

        {/* Tổng trị giá */}
        <div
          style={{
            marginTop: '16px',
            padding: '12px 16px',
            backgroundColor: '#f5f5f5',
            borderRadius: '6px',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            fontWeight: 'bold',
          }}
        >
          <span style={{ fontSize: '16px' }}>Tổng trị giá:</span>
          <span style={{ fontSize: '18px', color: '#1890ff' }}>{formatMoneyVND(calculateTotalValue())}</span>
        </div>
      </Card>
      <Divider />
      <Card title="Điều kiện báo giá">
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item label="Thời gian giao hàng" name="deliveryDate">
              <DatePicker placeholder="Chọn ngày giao hàng" format="YYYY-MM-DD" style={{ width: '100%' }} />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="Địa điểm giao hàng" name="deliveryLocation">
              <Input placeholder="Nhập địa điểm giao hàng" />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item
              label="Hiệu lực báo giá (ngày)"
              name="validityDays"
              required
              rules={[{ required: true, message: 'Vui lòng nhập số ngày hiệu lực' }]}
            >
              <InputNumber min={1} placeholder="Nhập số ngày hiệu lực" style={{ width: '100%' }} />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="File đính kèm" name="file">
              <Upload beforeUpload={() => false} accept=".pdf">
                <Button>Chọn tệp</Button>
              </Upload>
            </Form.Item>
          </Col>

          {/* <Col span={8}>
            <Form.Item label="Tổng trị giá báo giá">
              <Input
                value={formatMoneyVND(calculateTotalValue())}
                disabled
                style={{
                  fontWeight: 'bold',
                  fontSize: '16px',
                  color: '#1890ff',
                }}
              />
            </Form.Item>
          </Col> */}

          <Col span={24}>
            <Form.Item label="Ghi chú" name="notes">
              <Input.TextArea rows={3} placeholder="Nhập ghi chú" />
            </Form.Item>
          </Col>
        </Row>
      </Card>
      {/* Trường ẩn để lưu tổng trị giá */}
      <Form.Item name="totalAmount" style={{ display: 'none' }}>
        <Input type="hidden" />
      </Form.Item>
      <div
        style={{
          textAlign: 'right',
          paddingTop: 24,
        }}
      >
        <Button onClick={closeModal} style={{ marginRight: 8 }}>
          Hủy
        </Button>
        <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={isSubmitting}>
          Lưu
        </Button>
      </div>
    </Form>
  );

  const modalContentAddProduct = (
    <div>
      <BaseTable
        columns={columnsAddProduct}
        data={catalogItem}
        total={totalCatalogItem}
        isLoading={isLoadingCatalogItem}
        scroll={{ x: 'max-content' }}
        onPageChange={(pageIndex, pageSize) => {
          setPageCatalogItem({ pageIndex, pageSize });
        }}
      />
      <div style={{ textAlign: 'right', marginTop: 16 }}>
        <Button onClick={() => setOpenAddProductModal(false)} style={{ marginRight: 8 }}>
          Hủy
        </Button>
      </div>
    </div>
  );

  return (
    <>
      <BaseButton icon={<EditOutlined />} onClick={openModal} type="primary" tooltip="Chỉnh sửa báo giá" />
      <BaseModal
        open={open}
        title="Chỉnh sửa báo giá"
        description="Chỉnh sửa báo giá mới vào hệ thống"
        onClose={closeModal}
        width={2000}
        childrenBody={modalContent}
      />
      <BaseModal
        open={openAddProductModal}
        title="Thêm hàng hoá"
        description="Thêm hàng hoá vào hệ thống"
        onClose={() => setOpenAddProductModal(false)}
        width={1200}
        childrenBody={modalContentAddProduct}
      />
    </>
  );
};

export default QuotationEditComponent;
