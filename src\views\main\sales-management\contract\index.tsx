import { EditOutlined, EyeOutlined, PlusOutlined, SendOutlined } from '@ant-design/icons';
import { Button, notification, Space, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { IContract, ICreateContractDto, IPaginationContractDto } from '~/api/contract/types';
import { IQuotation } from '~/api/quotation/types';
import BaseCard from '~/components/base-card';
import BaseTable from '~/components/base-table';
import useTemplateContract from '../../setting/template-contract/hook/use-template-contract';
import { useQuotation } from '../quotation/Hooks/useQuotation';

import { NSContract } from '~/common/enums/contract.enum';
import BaseFilter from '~/components/base-filter/BaseFilter';
import CreateContractDrawer from './components/create-contract-drawer';
import DetailContractDrawer from './components/detail-contract-drawer';
import UpdateContractDrawer from './components/update-contract-drawer';
import useContract from './hook/use-contract';

export const ContractsComponent = () => {
  const { t } = useTranslation();

  const [visibleCreateContractDrawer, setVisibleCreateContractDrawer] = useState<boolean>(false);
  const [selectContract, setSelectContract] = useState<IContract | null>(null);
  const [visibleDetailContractDrawer, setVisibleDetailContractDrawer] = useState<boolean>(false);
  const [visibleUpdateContractDrawer, setVisibleUpdateContractDrawer] = useState<boolean>(false);
  const { data, loadData, createContract, updateContract, setFilter } = useContract();
  const { data: templateContract } = useTemplateContract();
  const { listQuotation } = useQuotation();
  const [quotation, setQuotation] = useState<IQuotation[]>([]);

  useEffect(() => {
    const getQuotation = async () => {
      const res = await listQuotation({
        pageIndex: 1,
        pageSize: 10,
      });
      const list = res.data.filter(item => item.status === 'CONFIRMED');
      setQuotation(list || []);
    };
    getQuotation();
  }, []);

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'NEWLY_CREATED':
        return <Tag color="blue">Mới tạo</Tag>;
      case 'DRAFT':
        return <Tag color="purple">Nháp</Tag>;
      case 'SENT':
        return <Tag color="green">Đã gửi</Tag>;
      case 'SIGNED':
        return <Tag color="orange">Đã ký</Tag>;
      case 'REJECTED':
        return <Tag color="red">Từ chối</Tag>;
      default:
        return <Tag color="default">Chờ xử lý</Tag>;
    }
  };
  const contractStatusOptions = Object.values(NSContract.EStatus).map(item => {
    const map: Record<string, string> = {
      NEWLY_CREATED: 'Mới tạo',
      DRAFT: 'Nháp',
      SENT: 'Đã gửi',
      SIGNED: 'Đã ký',
      REJECTED: 'Từ chối',
    };
    return {
      name: map[item] || item,
      value: item,
    };
  });
  const contractTypeOptions = Object.values(NSContract.EType).map(item => {
    const map: Record<string, string> = {
      SALES: 'Hợp đồng bán hàng',
      SERVICE: 'Hợp đồng dịch vụ',
      MAINTENANCE: 'Hợp đồng bảo trì',
      CONSULTATION: 'Hợp đồng tư vấn',
      DISTRIBUTION: 'Hợp đồng phân phối',
      PURCHASE: 'Hợp đồng mua hàng',
    };
    return {
      name: map[item] || item,
      value: item,
    };
  });

  const validateHidden = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return false;
      case 'SENT':
        return false;
      case 'SIGNED':
        return true;
      default:
        return false;
    }
  };

  const handleCreateContract = async (values: ICreateContractDto) => {
    try {
      await createContract(values);
      notification.success({
        message: 'Tạo hợp đồng thành công',
      });
      setVisibleCreateContractDrawer(false);
      loadData();
    } catch (error) {
      notification.error({
        message: error.message,
      });
    }
  };
  const handleUpdateContract = async (values: IContract) => {
    try {
      await updateContract(values);
      notification.success({
        message: 'Cập nhật hợp đồng thành công',
      });
      setVisibleUpdateContractDrawer(false);
      loadData();
    } catch (error) {
      notification.error({
        message: error.message,
      });
    }
  };
  const handleFilter = (values: any) => {
    setFilter(values);
  };

  const handleReset = () => {
    setFilter({} as IPaginationContractDto);
    loadData();
  };

  const columns: ColumnsType<IContract> = [
    {
      title: 'Mã hợp đồng',
      dataIndex: 'code',
      key: 'code',
    },
    // Số ký hợp đồng
    {
      title: 'Số ký hợp đồng',
      dataIndex: 'contractNumber',
      key: 'contractNumber',
    },
    {
      title: 'Tên hợp đồng',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Loại hợp đồng',
      dataIndex: 'type',
      key: 'type',
      render: type => contractTypeOptions.find(item => item.value === type)?.name || '--',
    },
    {
      title: 'Ngày ký',
      dataIndex: 'signingDate',
      key: 'signingDate',
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdDate',
      key: 'createdDate',
      render: createdDate => (createdDate ? dayjs(createdDate).format('YYYY-MM-DD') : '--'),
    },
    {
      title: 'Tạo từ báo giá',
      dataIndex: 'quoteId',
      key: 'quoteId',
      render: quoteId => {
        const quote = quotation.find(item => item.id === quoteId);
        return quote?.quotationNumber || '--';
      },
    },
    {
      title: 'Tạo từ mẫu',
      dataIndex: 'templateId',
      key: 'templateId',
      render: templateId => {
        const template = templateContract.find(item => item.id === templateId);
        return template?.name || '--';
      },
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: status => getStatusTag(status),
    },
    {
      title: 'Tác vụ',
      key: 'actions',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Button
            type="primary"
            icon={<EyeOutlined />}
            onClick={() => {
              setSelectContract(record);
              setVisibleDetailContractDrawer(true);
            }}
          />
          <Button
            icon={<EditOutlined />}
            disabled={validateHidden(record.status)}
            onClick={() => {
              setSelectContract(record);
              setVisibleUpdateContractDrawer(true);
            }}
          />
          <Button icon={<SendOutlined />}></Button>
        </Space>
      ),
    },
  ];
  const baseFilters = [
    {
      key: 'code',
      name: 'Mã hợp đồng',
      type: 'input',
    },
    {
      key: 'contractNumber',
      name: 'Số ký hợp đồng',
      type: 'input',
    },
    {
      key: 'name',
      name: 'Tên hợp đồng',
      type: 'input',
    },
    {
      key: 'type',
      name: 'Loại hợp đồng',
      type: 'select',
      selectOptions: contractTypeOptions,
    },
    {
      key: 'status',
      name: 'Trạng thái',
      type: 'select',
      selectOptions: contractStatusOptions,
    },
  ];
  return (
    <BaseCard
      title="Danh sách hợp đồng"
      buttons={[
        {
          text: 'Thêm mới hợp đồng',
          icon: <PlusOutlined />,
          onClick: () => {
            setVisibleCreateContractDrawer(true);
          },
          type: 'primary',
        },
      ]}
    >
      {visibleCreateContractDrawer && (
        <CreateContractDrawer
          open={visibleCreateContractDrawer}
          onClose={() => setVisibleCreateContractDrawer(false)}
          onSubmit={handleCreateContract}
          options={{
            quote: {
              options: quotation,
            },
            template: {
              options: templateContract,
            },
          }}
          length={data.length}
        />
      )}
      <DetailContractDrawer
        open={visibleDetailContractDrawer}
        onClose={() => setVisibleDetailContractDrawer(false)}
        data={selectContract}
        options={{
          quote: {
            options: quotation,
          },
          template: {
            options: templateContract,
          },
        }}
      />
      {visibleUpdateContractDrawer && (
        <UpdateContractDrawer
          open={visibleUpdateContractDrawer}
          onClose={() => setVisibleUpdateContractDrawer(false)}
          onSubmit={handleUpdateContract}
          data={selectContract}
          options={{
            quote: {
              options: quotation,
            },
            template: {
              options: templateContract,
            },
          }}
        />
      )}
      <BaseFilter onFilter={handleFilter} onReset={handleReset} isLoading={false} filters={baseFilters} />
      <BaseTable columns={columns} data={data} pagination={{ pageSize: 10 }} total={data.length} isLoading={false} />
    </BaseCard>
  );
};
