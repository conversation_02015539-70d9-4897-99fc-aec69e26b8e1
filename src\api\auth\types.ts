import type { AppRouteRecordRaw } from '~/router/types';

export interface PasswordLoginReq {
  username: string;
  password: string;
}

export interface RefreshTokenResult {
  token: string;
  refreshToken: string;
}

export interface AuthType {
  accessToken: string;
  refreshToken: string;
}

export interface UserInfoType {
  // id: string;
  // avatar: string;
  // username: string;
  // email: string;
  // phoneNumber: string;
  // description: string;
  // roles?: Array<string>;
  // // Routes can be dynamically added here
  // menus?: AppRouteRecordRaw[];

  id: string;
  username: string;
  fullName: string;
  avatar: string;
  tenantId: string;
  tenantInfo: {
    id: string;
    name: string;
    domain: string;
    logoUrl: string;
    website: string;
    address: string;
  };
  roles?: Array<string>;
  menus?: AppRouteRecordRaw[];
}

export interface AuthListProps {
  label: string;
  name: string;
  auth: string[];
}

export interface ForgotPasswordReq {
  email: string;
  username: string;
}
export interface ResetPasswordReq {
  accountId: string;
  newPassword: string;
  confirmNewPassword: string;
  expired: string;
}
