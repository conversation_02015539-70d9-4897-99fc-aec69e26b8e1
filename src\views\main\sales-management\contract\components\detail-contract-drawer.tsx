import { Col, Form, Input, Row, Select, Tag } from 'antd';
import { useEffect, useState } from 'react';
import { IContract } from '~/api/contract/types';
import { IQuotation } from '~/api/quotation/types';
import { IContractTemplate } from '~/api/setting/contract-template/types';
import { NSContract } from '~/common/enums/contract.enum';
import BaseDrawer from '~/components/base-drawer';
import RichTextEditor from '~/components/text-editor';
import { QuotationDetailComponent } from '../../quotation/components/quotation-detail-component';

export default function DetailContractDrawer({
  open,
  onClose,
  data,
  options,
}: {
  open: boolean;
  onClose: () => void;
  data: IContract | null;
  options?: {
    quote: {
      options: IQuotation[];
    };
    template: {
      options: IContractTemplate[];
    };
  };
}) {
  const [form] = Form.useForm();
  const [childrenContractDrawer, setChildrenContractDrawer] = useState(false);
  const [childrenQuoteDrawer, setChildrenQuoteDrawer] = useState(false);

  const onChildrenContractDrawerClose = () => {
    setChildrenContractDrawer(false);
  };

  const onChildrenQuoteDrawerClose = () => {
    setChildrenQuoteDrawer(false);
  };

  useEffect(() => {
    if (data) {
      form.setFieldsValue({
        contractNumber: data.contractNumber,
        code: data.code,
        name: data.name,
        type: data.type,
        html: data.html,
        quoteId: data.quoteId,
        templateId: data.templateId,
        signingDate: data.signingDate ? new Date(data.signingDate).toLocaleString('vi-VN') : '',
        status: data.status,
        createdDate: data.createdDate ? new Date(data.createdDate).toLocaleString('vi-VN') : '',
        updatedDate: data.updatedDate ? new Date(data.updatedDate).toLocaleString('vi-VN') : '',
      });
    }
  }, [data, form]);

  const contractTypeOptions = Object.values(NSContract.EType).map(item => {
    const map: Record<string, string> = {
      SALES: 'Hợp đồng bán hàng',
      SERVICE: 'Hợp đồng dịch vụ',
      MAINTENANCE: 'Hợp đồng bảo trì',
      CONSULTATION: 'Hợp đồng tư vấn',
      DISTRIBUTION: 'Hợp đồng phân phối',
      PURCHASE: 'Hợp đồng mua hàng',
    };
    return {
      label: map[item] || item,
      value: item,
    };
  });

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'NEWLY_CREATED':
        return <Tag color="blue">Mới tạo</Tag>;
      case 'DRAFT':
        return <Tag color="purple">Nháp</Tag>;
      case 'SENT':
        return <Tag color="green">Đã gửi</Tag>;
      case 'SIGNED':
        return <Tag color="orange">Đã ký</Tag>;
      case 'REJECTED':
        return <Tag color="red">Từ chối</Tag>;
      default:
        return <Tag color="default">Chờ xử lý</Tag>;
    }
  };

  return (
    <BaseDrawer
      title="Chi tiết hợp đồng"
      buttons={[
        {
          text: 'Xem hợp đồng',
          type: 'primary',
          onClick: () => setChildrenContractDrawer(true),
        },
        {
          text: 'Xem báo giá',
          type: 'primary',
          onClick: () => setChildrenQuoteDrawer(true),
        },
      ]}
      open={open}
      onClose={onClose}
      width="50%"
    >
      <Form layout="vertical" form={form} disabled>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="Số hợp đồng" name="contractNumber">
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Mã hợp đồng" name="code">
              <Input />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="Tên hợp đồng" name="name">
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Loại hợp đồng" name="type">
              <Select options={contractTypeOptions} />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="Mẫu hợp đồng" name="templateId">
              <Select
                placeholder="Chọn mẫu hợp đồng"
                options={options?.template?.options?.map(item => ({
                  label: item.name,
                  value: item.id,
                }))}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Báo giá" name="quoteId">
              <Select
                placeholder="Chọn báo giá"
                options={options?.quote?.options?.map(item => ({
                  label: item.quotationNumber,
                  value: item.id,
                }))}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="Ngày tạo" name="createdDate">
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Cập nhật lần cuối" name="updatedDate">
              <Input />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="Ngày ký" name="signingDate">
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Trạng thái">{getStatusTag(data?.status)}</Form.Item>
          </Col>
        </Row>
      </Form>
      <BaseDrawer
        title="Chi tiết hợp đồng"
        buttons={[
          {
            text: 'Đóng',
            onClick: onChildrenContractDrawerClose,
          },
        ]}
        open={childrenContractDrawer}
        onClose={onChildrenContractDrawerClose}
        width="50%"
      >
        <RichTextEditor value={data?.html} onChange={() => {}} readOnly={true} />
      </BaseDrawer>
      <BaseDrawer
        title="Báo giá"
        buttons={[
          {
            text: 'Đóng',
            onClick: onChildrenQuoteDrawerClose,
          },
        ]}
        open={childrenQuoteDrawer}
        onClose={onChildrenQuoteDrawerClose}
        width="50%"
      >
        
      </BaseDrawer>
    </BaseDrawer>
  );
}
