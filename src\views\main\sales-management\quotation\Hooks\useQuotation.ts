import { quotationApi } from '~/api/quotation';
import { IQuotation, IQuotationList, IQuotationUpdate } from '~/api/quotation/types';
import { UpdateStatusQuotationReq } from '~/api/sales-managerment/quotation-product/types';

export const useQuotation = () => {
  const listQuotation = (params: IQuotationList) => {
    return quotationApi.list(params);
  };

  const createQuotation = (data: IQuotation) => {
    return quotationApi.create(data);
  };

  const updateQuotation = (data: IQuotationUpdate) => {
    return quotationApi.update(data);
  };

  const detailQuotation = (id: string) => {
    return quotationApi.detail({ id });
  };
  const updateStatusQuotation = (data: UpdateStatusQuotationReq) => {
    return quotationApi.updateStatus(data);
  };

  return {
    listQuotation,
    createQuotation,
    updateQuotation,
    detailQuotation,
    updateStatusQuotation,
  };
};
