export interface IWard {
  name: string;
  code: number;
  division_type: string;
  codename: string;
  district_code: number;
}

export interface IDistrict {
  name: string;
  code: number;
  division_type: string;
  codename: string;
  province_code: number;
  wards?: IWard[];
}

export interface IProvince {
  data: Array<{
    name: string;
    code: number;
    division_type: string;
    codename: string;
    phone_code: number;
  }>;
}

export interface IFullAddress {
  province: IProvince;
  district: IDistrict;
  ward: IWard;
  address?: string;
}

export class ListDistrictReq {
  code?: number;
}

export class ListWardReq {
  code: number;
}

export class ListProvinceReq {
  code?: number;
}

export class GetFullAddressReq {
  provinceCode: string;
  districtCode: string;
  wardCode: string;
  address?: string;
}
