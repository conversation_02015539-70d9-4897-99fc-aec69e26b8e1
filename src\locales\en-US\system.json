{"role": {"name": "Role Name", "id": "Role Id", "addRole": "Add Role", "editRole": "Edit Role", "assignMenu": "Assign <PERSON>"}, "user": {"name": "User Name", "id": "User Id", "addUser": "Add User", "editUser": "Edit User"}, "menu": {"name": "<PERSON>u Name", "routePath": "Route Path", "addMenu": "<PERSON><PERSON>", "editMenu": "<PERSON>", "menuType": "Menu Type", "menuTypeDesc": "Menu Type Description 0: <PERSON><PERSON>, 1: <PERSON><PERSON><PERSON>, 2: <PERSON> Link, 3: <PERSON><PERSON>", "menu": "<PERSON><PERSON>", "iframe": "<PERSON><PERSON><PERSON>", "externalLink": "External Link", "button": "<PERSON><PERSON>", "menuOrder": "Menu Order", "menuIcon": "Menu Icon", "componentUrl": "Component Url", "keepAlive": "<PERSON><PERSON>", "hideInMenu": "Hide in Menu", "currentActiveMenu": "Active Path", "iframeLink": "Iframe Link", "externalLinkUrl": "External Link URL", "parentMenu": "<PERSON><PERSON>"}, "dept": {"name": "Department Name", "id": "Department Id", "addDept": "Add Department", "editDept": "Edit Department"}}