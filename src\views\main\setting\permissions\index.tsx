import { Button, Checkbox, Col, Row, Select } from 'antd';
import { useState } from 'react';
import { IPermission, IPermissionFilter } from '~/api/setting/permission/types';
import BaseCard from '~/components/base-card';
import BaseTable from '~/components/base-table';
import FilterProduct from './components/filter-product';
import MemberSelect from '~/components/member-select';

export const PermissionsView = () => {
  const [filter, setFilter] = useState<IPermissionFilter>({
    id: null,
    name: '',
    pageIndex: 0,
    pageSize: 10,
  });
  const [memberId, setMemberId] = useState(null);

  const handleFilter = (values: IPermissionFilter) => {
    setFilter(values);
  };

  const handleReset = () => {
    setFilter({
      id: null,
      name: '',
      pageIndex: 0,
      pageSize: 10,
    });
  };

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({
      ...filter,
      pageIndex: newPageIndex,
      pageSize: newPageSize,
    });
  };

  const handleMemberChange = (value: string) => {
    setMemberId(value);
  }

  const columns: any[] = [
    {
      title: 'Tên Quyền',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Xem',
      dataIndex: 'view',
      key: 'view',
      align: 'center',
      render: (_, record: IPermission) => <Checkbox checked={record.canView} />,
    },
    {
      title: 'Thêm',
      dataIndex: 'add',
      key: 'add',
      align: 'center',
      render: (_, record: IPermission) => <Checkbox checked={record.canCreate} />,
    },
    {
      title: 'Chỉnh sửa',
      dataIndex: 'edit',
      key: 'edit',
      align: 'center',
      render: (_, record: IPermission) => <Checkbox checked={record.canUpdate} />,
    },
  ];

  const fakeData = [
    {
      id: '1',
      name: 'Phân tích',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '2',
      name: 'Báo cáo tổng hợp khách hàng',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '3',
      name: 'Doanh số khách hàng',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '4',
      name: 'Báo cáo khách hàng theo nhân viên kinh doanh',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '5',
      name: 'Danh sách khách hàng',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '6',
      name: 'Danh sách liên hệ',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '7',
      name: 'Khách hàng chưa định danh',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '8',
      name: 'Đánh giá khách hàng',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '9',
      name: 'Đánh giá liên hệ',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '10',
      name: 'Báo giá',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '11',
      name: 'Hợp đồng',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '12',
      name: 'Hóa đơn',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '13',
      name: 'Quản lý sản phẩm',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '14',
      name: 'Thăm hỏi khách hàng',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '15',
      name: 'Nhiệm vụ',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '16',
      name: 'Xử lý khiếu nại',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '17',
      name: 'Danh sách khảo sát',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '18',
      name: 'Báo cáo kết quả khảo sát',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '19',
      name: 'Danh sách quà tặng',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '20',
      name: 'Quản lý đổi thưởng',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '21',
      name: 'Lịch sử đổi thưởng',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '22',
      name: 'Nhóm mục tiêu',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '23',
      name: 'Quản lý nội dung',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '24',
      name: 'Quản lý chiến dịch',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '25',
      name: 'Nhân viên',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '26',
      name: 'Phòng ban',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '27',
      name: 'Phân quyền',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '28',
      name: 'Tiêu chí đánh giá khách hàng',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '29',
      name: 'Báo giá',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '30',
      name: 'Hợp đồng',
      view: true,
      add: true,
      edit: true,
    },
    {
      id: '31',
      name: 'Hóa đơn',
      view: true,
      add: true,
      edit: true,
    },
  ];
  return (
    <BaseCard title="Thiết lập phân quyền">
      <Row gutter={16}>
        <Col span={24}>
          <FilterProduct onFilter={handleFilter} onReset={handleReset} isLoading={false} />
        </Col>
      </Row>
      <Row gutter={16} style={{ marginTop: 16 }}>
        <Col span={16}>
          <Row gutter={16}>
            <Col
              span={1.5}
              style={{
                display: 'flex',
                alignItems: 'center', // căn giữa theo chiều dọc
                justifyContent: 'center', // căn giữa theo chiều ngang
              }}
            >
              <label>Nhân viên</label>
            </Col>
            <Col span={6}>
              <MemberSelect onChange={handleMemberChange} value={memberId} />
            </Col>
          </Row>
        </Col>

        <Col span={8} style={{ textAlign: 'right' }}>
          <Button type="primary">Lưu</Button>
        </Col>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable columns={columns} data={fakeData} total={0} isLoading={false} onPageChange={handlePageChange} />
        </Col>
      </Row>
    </BaseCard>
  );
};
