import { rootApiConnector } from '~/connectors';
import { IPageResponse } from '../@common';
import { ICustomer, ListCustomerParams } from './types';

export const ENDPOINT = {
  LIST: 'api/client/customer/list',
  CREATE: 'api/client/customer/create',
  UPDATE: 'api/client/customer/update',
  DETAIL: 'api/client/customer/details',
  //setActuve
  SET_ACTIVE: 'api/client/customer/set-active',
};
class CustomerApi {
  list = (params: ListCustomerParams) => {
    return rootApiConnector.get<IPageResponse<ICustomer>>(ENDPOINT.LIST, params);
  };

  //create
  create = (params: ICustomer) => {
    return rootApiConnector.post(ENDPOINT.CREATE, params);
  };

  //update
  update = (params: ICustomer) => {
    return rootApiConnector.post(ENDPOINT.UPDATE, params);
  };

  detail = (params: { id: string }) => {
    return rootApiConnector.get(ENDPOINT.DETAIL, params);
  };

  setActive = (params: { id: string }) => {
    return rootApiConnector.post(ENDPOINT.SET_ACTIVE, params);
  };
}
export const customerApi = new CustomerApi();
