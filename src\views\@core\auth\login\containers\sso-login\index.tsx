import { Button, ConfigProvider, Layout, Space, theme } from 'antd';
import Title from 'antd/es/typography/Title';
import { authApi } from '~/api/auth';
import { loginAssets } from '~/assets';
import { COLORS } from '~/common/constants/colors';

// Styles với màu cố định
const styles: Record<string, React.CSSProperties> = {
  layout: {
    height: '100vh',
    justifyContent: 'center',
    alignItems: 'flex-end',
    backgroundImage: `url(${loginAssets.loginBackground})`,
    backgroundSize: 'cover',
    backgroundRepeat: 'no-repeat',
    backgroundPosition: 'center',
    paddingRight: 100,
    backgroundColor: COLORS.WHITE, // Đảm bảo background luôn trắng
  },
  formContainer: {
    backgroundColor: COLORS.WHITE,
    padding: '20px 24px',
    borderRadius: 12,
    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
    width: 360,
    color: COLORS.BLACK, // <PERSON><PERSON>m bảo text luôn đen
  },
  title: {
    textAlign: 'center',
    marginBottom: 24,
    color: COLORS.PRIMARY_2,
    fontSize: '30px',
  },
  submitButton: {
    backgroundColor: COLORS.PRIMARY_2,
    borderColor: COLORS.PRIMARY_2,
    width: '100%',
    height: 40,
  },
  formItem: {
    marginBottom: 16,
  },
};

const loginThemeConfig = {
  algorithm: theme.defaultAlgorithm,
  token: {
    colorPrimary: COLORS.PRIMARY_RGB,
    colorBgContainer: COLORS.WHITE,
    colorBgBase: COLORS.WHITE,
    colorText: COLORS.BLACK,
    colorTextBase: COLORS.BLACK,
  },
  components: {
    Layout: {
      colorBgHeader: COLORS.WHITE,
      colorBgBody: COLORS.WHITE,
    },
    Input: {
      colorBgContainer: COLORS.WHITE,
      colorText: COLORS.BLACK,
    },
    Button: {
      colorPrimary: COLORS.PRIMARY_2,
    },
    Form: {
      labelColor: COLORS.BLACK,
    },
  },
};
export const SSOLogin = () => {
  const handleApetechsLogin = async () => {
    try {
      await authApi.ssoLogin();
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <ConfigProvider theme={loginThemeConfig}>
      <Layout style={styles.layout}>
        <div style={styles.formContainer}>
          <Title style={styles.title}>Chào mừng trở lại</Title>
          <Space style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Button type="primary" onClick={handleApetechsLogin}>
              Login by APE
            </Button>
          </Space>
        </div>
      </Layout>
    </ConfigProvider>
  );
};
