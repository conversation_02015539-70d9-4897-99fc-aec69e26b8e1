import { Button, Popconfirm, Tooltip } from 'antd';
import type { ButtonType } from 'antd/es/button';
import React from 'react';

interface BaseButtonProps {
  icon?: React.ReactNode;
  tooltip?: string;
  type?: ButtonType;
  danger?: boolean;
  children?: React.ReactNode;
  shape?: 'circle' | 'round';
  htmlType?: 'submit' | 'reset' | 'button';
  loading?: boolean;
  confirmTitle?: string;
  onConfirm: () => void;
  size?: 'large' | 'middle' | 'small';
}

const BaseButton: React.FC<BaseButtonProps> = ({
  icon,
  tooltip = '',
  type = 'text',
  danger = false,
  children,
  loading,
  confirmTitle = '',
  onConfirm,
  size = 'small',
}) => {
  const BtnComponent = (
    <Popconfirm title={confirmTitle} onConfirm={onConfirm}>
      <Button type={type} icon={icon} danger={danger} loading={loading} size={size}>
        {!icon && children}
      </Button>
    </Popconfirm>
  );

  return tooltip ? <Tooltip title={tooltip}>{BtnComponent}</Tooltip> : BtnComponent;
};

export default BaseButton;
