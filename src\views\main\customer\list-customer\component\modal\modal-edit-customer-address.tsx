import { SaveOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Card, Col, Form, Input, Row, Select } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useEffect } from 'react';
import BaseModal from '~/components/base-modal';

interface ModalCustomerAddressProps {
  open: boolean;
  onClose: () => void;
  data: any;
  style: any;
  onSave: (values: any) => Promise<void>;
}
export const ModalEditCustomerAddress = (props: ModalCustomerAddressProps) => {
  const [form] = useForm();
  const { open, onClose, data, style, onSave } = props;
  useEffect(() => {
    if (data) {
      form.setFieldsValue(data);
    }
  }, [data, form, open]);
  const handleUpdate = async (values: any) => {
    try {
      const body = {
        ...data,
        ...values,
      };
      onSave(body).then(() => {
        onClose();
      });
    } catch (error) {}
  };
  const modalContent = open && (
    <div>
      <Form form={form} layout="vertical" onFinish={handleUpdate}>
        <Card title="Thông tin địa chỉ">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Tên địa chỉ" name="addressName">
                <Select allowClear placeholder="Chọn tên địa chỉ">
                  <Select.Option value="Nhà riêng" key="Nhà riêng">
                    Nhà riêng
                  </Select.Option>
                  <Select.Option value="Công ty" key="Công ty">
                    Công ty
                  </Select.Option>
                  <Select.Option value="Cơ quan" key="Cơ quan">
                    Cơ quan
                  </Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Địa chỉ" name="address">
                <Input value={data.address} placeholder="Nhập Địa chỉ" />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="Ghi chú" name="description">
                <Input.TextArea rows={3} placeholder="Nhập Ghi chú" />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        <div
          style={{
            textAlign: 'center',
            marginTop: 24,
            paddingTop: 16,
          }}
        >
          <Button onClick={onClose} style={{ marginRight: 8 }}>
            Hủy
          </Button>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
            Cập nhật
          </Button>
        </div>
      </Form>
    </div>
  );
  return (
    <BaseModal
      width={style.width}
      open={open}
      onClose={onClose}
      title="Chỉnh sửa thông tin địa chỉ"
      description="Cập nhật thông tin địa chỉ khách hàng"
      childrenBody={modalContent}
    />
  );
};
