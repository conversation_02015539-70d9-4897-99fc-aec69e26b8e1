import { Col, Row } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { formatMoneyVND } from '~/common/helpers/helper';

import BaseTable from '~/components/base-table';
import BaseView from '~/components/base-view';

interface IRevenue {
  id: string;
  code: string;
  createdBy: string;
  createdAt: string;
  revenue: number;
  debt: number;
}
// Mock data for demonstration
const mockDocuments: IRevenue[] = [
  {
    id: '1',
    code: '1234567890',
    createdBy: 'Nguyễn Văn A',
    createdAt: '2024-03-15T08:30:00',
    revenue: 100000000,
    debt: 100000000,
  },
  {
    id: '2',
    code: '1234567890',
    createdBy: 'Trần Thị B',
    createdAt: '2024-03-14T14:20:00',
    revenue: 100000000,
    debt: 100000000,
  },
  {
    id: '3',
    code: '1234567890',
    createdBy: '<PERSON><PERSON> Văn C',
    createdAt: '2024-03-13T10:15:00',
    revenue: 100000000,
    debt: 100000000,
  },
  {
    id: '4',
    code: '1234567890',
    createdBy: 'Phạm Thị D',
    createdAt: '2024-03-12T16:45:00',
    revenue: 100000000,
    debt: 100000000,
  },
  {
    id: '5',
    code: '1234567890',
    createdBy: 'Hoàng Văn E',
    createdAt: '2024-03-11T09:30:00',
    revenue: 100000000,
    debt: 100000000,
  },
];

export const RevenueTab = () => {
  // Using mock data instead of API call
  const data = { data: mockDocuments, total: mockDocuments.length };
  const isLoading = false;

  const columns: ColumnsType<IRevenue> = [
    {
      title: 'STT',
      key: 'stt',
      width: 60,
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Mã đơn bán',
      dataIndex: 'code',
      key: 'code',
      width: 150,
    },
    {
      title: 'Người tạo',
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 150,
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      align: 'center',
      render: (value: string) => (value ? new Date(value).toLocaleDateString('vi-VN') : '-'),
    },
    {
      title: 'Doanh số',
      dataIndex: 'revenue',
      key: 'revenue',
      width: 150,
      render: (value: number) => formatMoneyVND(value),
    },
    {
      title: 'Doanh thu',
      dataIndex: 'revenue',
      key: 'revenue',
      width: 150,
      render: (value: number) => formatMoneyVND(value),
    },
    {
      title: 'Công nợ',
      dataIndex: 'debt',
      key: 'debt',
      width: 150,
      render: (value: number) => formatMoneyVND(value),
    },
  ];

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={data?.data || []}
            total={data?.total || 0}
            isLoading={isLoading}
            scroll={{ x: 1000 }}
          />
        </Col>
      </Row>
    </BaseView>
  );
};
