import { SaveOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd';
import {
  <PERSON><PERSON>,
  Card,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  InputNumber,
  notification,
  Row,
  Select,
  Upload,
} from 'antd';
import { useForm } from 'antd/es/form/Form';
import { ColumnsType } from 'antd/es/table';
import { TableRowSelection } from 'antd/es/table/interface';
import { Table } from 'antd/lib';
import dayjs from 'dayjs';
import { useCallback, useEffect, useState } from 'react';
import { ICustomer } from '~/api/customer/types';
import { formatMoneyVND } from '~/common/helpers/helper';
import BaseModal from '~/components/base-modal';
import BaseTable from '~/components/base-table';
import { useCatalogItem } from '~/views/main/setting/catalog-item/hook/useCatalogItem';
import { useQuotation } from '../Hooks/useQuotation';
import useCustomer from '~/views/main/customer/Hooks/useCustomer';
import useCustomerContact from '~/views/main/customer/Hooks/useCustomerContact';
import { ICustomerContact } from '~/api/customer-care/customer-contact/types';

const { Option } = Select;

interface CreateQuotationModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const CreateQuotationModal = ({ open, onClose, onSuccess }: CreateQuotationModalProps) => {
  const {
    data: catalogItem,
    total: totalCatalogItem,
    setPage: setPageCatalogItem,
    isLoading: isLoadingCatalogItem,
  } = useCatalogItem();
  const [customerData, setCustomerData] = useState<ICustomer[]>([]);
  const [customerContactData, setCustomerContactData] = useState<any[]>([]);

  const [form] = useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [openAddProductModal, setOpenAddProductModal] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<any[]>([]);
  const [quotationProducts, setQuotationProducts] = useState<any[]>([]);

  const { listCustomer } = useCustomer();
  const { createQuotation } = useQuotation();
  const { listCustomerContact } = useCustomerContact();

  const closeModal = () => {
    onClose();
    form.resetFields();
    setQuotationProducts([]);
    setSelectedRowKeys([]);
    setSelectedProducts([]);
  };
  const initData = async () => {
    try {
      const [customer] = await Promise.all([listCustomer({})]);
      setCustomerData(customer.data);
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  useEffect(() => {
    initData();
  }, []);
  // Tính tổng trị giá
  const calculateTotalValue = useCallback(() => {
    return quotationProducts.reduce((total, product) => {
      const totalAfterVat = product.totalAfterVat || 0;
      return total + (typeof totalAfterVat === 'string' ? parseFloat(totalAfterVat) : totalAfterVat);
    }, 0);
  }, [quotationProducts]);

  // Cập nhật tổng trị giá trong form mỗi khi quotationProducts thay đổi
  useEffect(() => {
    const totalAmount = calculateTotalValue();
    form.setFieldValue('totalAmount', totalAmount);
  }, [quotationProducts, form, calculateTotalValue]);

  useEffect(() => {
    form.setFieldsValue({
      quotationDate: dayjs(),
      deliveryDate: dayjs().add(7, 'day'),
    });
  }, [open, form]);

  const handleSubmit = async (values: any) => {
    if (!values) return;

    setIsLoading(true);
    createQuotation({ ...values, quotationProducts })
      .then(res => {
        if (res) {
          setIsLoading(false);
          closeModal();
          notification.success({
            message: res.message,
            placement: 'top',
          });
        }
      })
      .catch(err => {
        setIsLoading(false);
        notification.error({
          message: err.message,
          placement: 'top',
        });
      })
      .finally(() => {
        setIsLoading(false);
        closeModal();
        onSuccess?.();
      });
  };

  // Xử lý khi chọn khách hàng
  const handleCustomerChange = (value: string) => {
    const customer = customerData.find(c => c.id === value);
    listCustomerContact({ customerId: value }).then(res => {
      setCustomerContactData(res.data);
    });
    form.setFieldsValue({
      memberId: undefined,
      customerAddress: customer?.addressLine || '',
      customerTaxCode: customer?.taxNumber || '',
      customerContactPerson: undefined,
      customerPhone: customer?.phone,
    });
  };

  // Xử lý khi chọn contact
  const handleContactChange = (value: string) => {
    const contact = customerContactData.find(c => c.id === value);
    form.setFieldValue('customerPhone', contact?.phone || '');
  };

  const onSelectChange = (newSelectedRowKeys: React.Key[], selectedRows: any[]) => {
    setSelectedRowKeys(newSelectedRowKeys);

    // Tính toán các giá trị cho sản phẩm được chọn
    const calculatedProducts = selectedRows.map(product => {
      const quantity = product.quantity || 1;
      const unitPrice = product.unitPrice || 0;
      const vat = product.vat || 10;
      const totalBeforeVat = quantity * unitPrice;
      const totalAfterVat = Math.round(quantity * unitPrice * (1 + vat / 100));

      return {
        ...product,
        quantity,
        unitPrice,
        vat,
        totalBeforeVat,
        totalAfterVat,
      };
    });

    setSelectedProducts(calculatedProducts);
  };

  const rowSelection: TableRowSelection<any> = {
    selectedRowKeys,
    onChange: onSelectChange,
    selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT, Table.SELECTION_NONE],
  };

  const handleAddSelectedProducts = () => {
    // Thay thế toàn bộ quotationProducts bằng selectedProducts mới
    setQuotationProducts([...selectedProducts]);
    setSelectedRowKeys([]);
    setSelectedProducts([]);
    setOpenAddProductModal(false);
  };

  // Xử lý thêm sản phẩm mới
  const handleAddItem = (catalog: any) => {
    const newItem = {
      catalogId: catalog.id,
      catalogItemId: catalog.id,
      code: catalog.code,
      name: catalog.name,
      unit: catalog.unit || 'Không có',
      quantity: 1,
      unitPrice: catalog.unitPrice,
      totalBeforeVat: catalog.unitPrice,
      vat: 10,
      totalAfterVat: Math.round(catalog.unitPrice * 1.1),
      type: catalog.type || 'Không có',
      description: catalog.description,
      currency: catalog.currency || 'Không có',
      images: catalog.images,
      attachments: catalog.attachments,
      status: catalog.status,
    };
    //check item trùng thì báo lỗi
    const isDuplicate = quotationProducts.find(item => item.catalogId === newItem.catalogId);
    if (isDuplicate) {
      notification.warning({
        message: 'Sản phẩm đã tồn tại',
        placement: 'top',
      });
      return;
    }

    const newItems = [...quotationProducts, newItem];
    setQuotationProducts(newItems);
    setOpenAddProductModal(false);
  };

  const columns: ColumnsType<any> = [
    {
      title: 'STT',
      dataIndex: 'stt',
      align: 'center' as const,
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Mã sản phẩm',
      dataIndex: 'code',
    },
    {
      title: 'Sản phẩm',
      dataIndex: 'name',
    },
    {
      title: 'Đơn vị',
      dataIndex: 'unit',
    },
    {
      title: 'Số lượng',
      dataIndex: 'quantity',
      render: (value, record, index) => (
        <InputNumber
          min={1}
          defaultValue={value}
          formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.')}
          onChange={val => {
            const newProducts = [...quotationProducts];
            const quantity = parseInt(val) || 1;
            const unitPrice = parseInt(newProducts[index].unitPrice) || 0;
            const vat = parseFloat(newProducts[index].vat) || 0;
            console.log('quantity', quantity);
            console.log('unitPrice', unitPrice);
            console.log('vat', vat);
            newProducts[index] = {
              ...newProducts[index],
              quantity,
              totalBeforeVat: quantity * unitPrice,
              totalAfterVat: Number(quantity * unitPrice * (1 + vat / 100)).toFixed(2),
            };
            setQuotationProducts(newProducts);
          }}
        />
      ),
    },
    {
      title: 'Đơn giá',
      dataIndex: 'unitPrice',
      render: (value, record, index) => (
        <InputNumber
          min={0}
          defaultValue={value || 0}
          style={{ width: '100%' }}
          formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.')}
          parser={value => (value ? value.replace(/\./g, '') : '')}
          onChange={val => {
            const newProducts = [...quotationProducts];
            const quantity = parseInt(newProducts[index].quantity) || 1;
            const unitPrice = parseInt(val) || 0;
            const vat = parseFloat(newProducts[index].vat) || 0;
            console.log('quantity', quantity);
            console.log('unitPrice', unitPrice);
            console.log('vat', vat);
            console.log('vat', quantity * unitPrice * (1 + vat / 100));
            newProducts[index] = {
              ...newProducts[index],
              unitPrice,
              totalBeforeVat: quantity * unitPrice,
              totalAfterVat: Number(quantity * unitPrice * (1 + vat / 100)).toFixed(2),
            };
            setQuotationProducts(newProducts);
          }}
        />
      ),
    },
    {
      title: 'Thành tiền chưa VAT',
      dataIndex: 'totalBeforeVat',
      render: value => formatMoneyVND(value),
    },
    {
      title: 'VAT (%)',
      dataIndex: 'vat',
      render: (value, record, index) => (
        <InputNumber
          min={0}
          max={100}
          defaultValue={value || 0}
          onChange={val => {
            const newProducts = [...quotationProducts];
            const quantity = parseInt(newProducts[index].quantity) || 1;
            const unitPrice = parseInt(newProducts[index].unitPrice) || 0;
            const vat = parseFloat(val) || 0;

            newProducts[index] = {
              ...newProducts[index],
              vat,
              totalAfterVat: Number(quantity * unitPrice * (1 + vat / 100)).toFixed(2),
            };
            setQuotationProducts(newProducts);
          }}
        />
      ),
    },
    {
      title: 'Thành tiền sau VAT',
      dataIndex: 'totalAfterVat',
      render: value => formatMoneyVND(value),
    },
    {
      title: 'Tác vụ',
      align: 'center' as const,
      fixed: 'right' as const,
      render: (_, __, index) => (
        <Button
          danger
          size="small"
          onClick={() => {
            const newProducts = quotationProducts.filter((_, i) => i !== index);
            setQuotationProducts(newProducts);
          }}
        >
          Xóa
        </Button>
      ),
    },
  ];

  const columnsAddProduct: ColumnsType<any> = [
    {
      title: 'STT',
      dataIndex: 'stt',
      key: 'stt',
      align: 'center',
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Mã sản phẩm',
      dataIndex: 'code',
      key: 'code',
      align: 'center',
    },
    {
      title: 'Sản phẩm',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: 'Đơn vị',
      dataIndex: 'unit',
      align: 'center',
    },

    {
      title: 'Tác vụ',
      render: (_, record) => {
        return (
          <Button type="primary" onClick={() => handleAddItem(record)}>
            Thêm
          </Button>
        );
      },
    },
  ];

  const modalContent = (
    <Form form={form} layout="vertical" onFinish={handleSubmit}>
      <Card title="Công ty">
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              label="Số báo giá"
              name="quotationNumber"
              rules={[{ required: true, message: 'Vui lòng nhập số báo giá' }]}
            >
              <Input placeholder="Nhập số báo giá" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="Ngày báo giá"
              name="quotationDate"
              required
              rules={[{ required: true, message: 'Vui lòng chọn ngày báo giá' }]}
            >
              <DatePicker placeholder="Ngày báo giá" format="YYYY-MM-DD" style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider />
      <Card title="Thông tin khách hàng">
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item label="Khách hàng" name="customerId" rules={[{ required: true }]}>
              <Select placeholder="Chọn khách hàng" onChange={handleCustomerChange} allowClear>
                {customerData.map(c => (
                  <Select.Option value={c.id}>{c.name}</Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="Địa chỉ" name="customerAddress">
              <Input placeholder="Địa chỉ khách hàng" disabled />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="Mã số thuế" name="customerTaxCode">
              <Input placeholder="Mã số thuế khách hàng" disabled />
            </Form.Item>
          </Col>

          <Col span={16}>
            <Form.Item
              label="Người liên hệ"
              name="memberId"
              required
              rules={[{ required: true, message: 'Vui lòng chọn người liên hệ' }]}
            >
              <Select
                placeholder="Chọn người liên hệ"
                onChange={handleContactChange}
                allowClear
                disabled={form.getFieldValue('customerId') === undefined}
              >
                {customerContactData.map(c => (
                  <Select.Option value={c.id}>{c.name}</Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="Điện thoại" name="customerPhone">
              <Input placeholder="Số điện thoại liên hệ" disabled />
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider />
      <Card
        title="Thông tin hàng hoá"
        extra={
          <Button
            type="primary"
            onClick={() => {
              // Khi mở modal, set lại selectedRowKeys và selectedProducts từ quotationProducts hiện tại
              const currentProductIds = quotationProducts.map(product => product.id);
              const currentSelectedProducts = quotationProducts.map(product => ({
                ...product,
                quantity: product.quantity || 1,
              }));
              setSelectedRowKeys(currentProductIds);
              setSelectedProducts(currentSelectedProducts);
              setOpenAddProductModal(true);
            }}
          >
            Thêm
          </Button>
        }
      >
        <BaseTable
          columns={columns}
          data={quotationProducts}
          total={quotationProducts?.length}
          isLoading={false}
          scroll={{ x: 'max-content' }}
        />

        {/* Tổng trị giá */}
        {quotationProducts && quotationProducts.length > 0 && (
          <div
            style={{
              marginTop: '16px',
              padding: '12px 16px',
              backgroundColor: '#f5f5f5',
              borderRadius: '6px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              fontWeight: 'bold',
            }}
          >
            <span style={{ fontSize: '16px' }}>Tổng trị giá:</span>
            <span style={{ fontSize: '18px', color: '#1890ff' }}>
              {formatMoneyVND(
                quotationProducts.reduce((total, product) => {
                  const totalAfterVat = product.totalAfterVat || 0;
                  return total + (typeof totalAfterVat === 'string' ? parseFloat(totalAfterVat) : totalAfterVat);
                }, 0),
              )}
            </span>
          </div>
        )}
      </Card>
      <Divider />
      <Card title="Điều kiện báo giá">
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item label="Thời gian giao hàng" name="deliveryDate">
              <DatePicker placeholder="Chọn ngày giao hàng" format="YYYY-MM-DD" style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="Địa điểm giao hàng"
              name="deliveryLocation"
            >
              <Input placeholder="Nhập địa điểm giao hàng" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="Hiệu lực báo giá (ngày)"
              name="validityDays"
              required
              rules={[{ required: true, message: 'Vui lòng nhập số ngày hiệu lực' }]}
            >
              <InputNumber min={1} placeholder="Nhập số ngày hiệu lực" style={{ width: '100%' }} />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="File đính kèm" name="file">
              <Upload beforeUpload={() => false} accept=".pdf">
                <Button>Chọn tệp</Button>
              </Upload>
            </Form.Item>
          </Col>

          {/* <Col span={8}>
            <Form.Item label="Tổng trị giá báo giá">
              <Input
                value={formatMoneyVND(calculateTotalValue())}
                disabled
                style={{
                  fontWeight: 'bold',
                  fontSize: '16px',
                  color: '#1890ff',
                }}
              />
            </Form.Item>
          </Col> */}
          <Col span={24}>
            <Form.Item label="Ghi chú" name="notes">
              <Input.TextArea rows={3} placeholder="Nhập ghi chú" />
            </Form.Item>
          </Col>
        </Row>
      </Card>

      {/* Trường ẩn để lưu tổng trị giá */}
      <Form.Item name="totalAmount" style={{ display: 'none' }}>
        <Input type="hidden" />
      </Form.Item>
      <div
        style={{
          textAlign: 'right',
          paddingTop: 24,
        }}
      >
        <Button onClick={onClose} style={{ marginRight: 8 }}>
          Hủy
        </Button>
        <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={isLoading}>
          Lưu
        </Button>
      </div>
    </Form>
  );

  const modalContentAddProduct = (
    <div>
      <BaseTable
        columns={columnsAddProduct}
        data={catalogItem}
        total={totalCatalogItem}
        isLoading={isLoadingCatalogItem}
        scroll={{ x: 'max-content' }}
        onPageChange={(pageIndex, pageSize) => {
          setPageCatalogItem({ pageIndex, pageSize });
        }}
      />
      <div style={{ textAlign: 'right', marginTop: 16 }}>
        <Button onClick={() => setOpenAddProductModal(false)} style={{ marginRight: 8 }}>
          Hủy
        </Button>
      </div>
    </div>
  );

  return (
    <>
      <BaseModal
        open={open}
        title="Tạo báo giá"
        description="Thêm báo giá mới vào hệ thống"
        onClose={onClose}
        width={2000}
        childrenBody={modalContent}
      />
      <BaseModal
        open={openAddProductModal}
        title="Thêm hàng hoá"
        description="Thêm hàng hoá vào hệ thống"
        onClose={() => setOpenAddProductModal(false)}
        width={1200}
        childrenBody={modalContentAddProduct}
      />
    </>
  );
};

export default CreateQuotationModal;
