import { IPageRequest } from '~/api/@common';
import { CommonEnum } from '~/common/enums';

export interface IContractTemplate {
  id: string;
  name: string;
  code: string;
  html: string;
  tenantId: string;
  status: CommonEnum.CommonStatus;
  updatedBy?: string;
  updatedDate?: Date;
  createdBy?: string;
  createdDate?: Date;
}
export interface IPaginationContractTemplate extends IPageRequest {
  name?: string;
  code?: string;
  description?: string;
  status?: CommonEnum.CommonStatus;
  createdBy?: string;
  createdDate?: Date;
}
export interface ICreateContractTemplate {
  name: string;
  code: string;
  html: string;
}
