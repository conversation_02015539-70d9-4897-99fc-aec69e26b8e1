import { EyeOutlined } from '@ant-design/icons';
import { Col, Row } from 'antd';
import moment from 'moment';
import { IDepartment, IListDepartment } from '~/api/setting/department/types';
import BaseButton from '~/components/base-button';
import BaseCard from '~/components/base-card';
import BaseFilter from '~/components/base-filter/BaseFilter';
import BaseTable from '~/components/base-table';
import { useDepartment } from './hook/useDepartment';

export const DepartmentView = () => {
  const { data, total, isLoading, page, setPage, loadData } = useDepartment();

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setPage({
      ...page,
      pageIndex: newPageIndex,
      pageSize: newPageSize,
    });
  };

  const handleFilter = (values: IListDepartment) => {
    if (values.createdDate) {
      if (Array.isArray(values.createdDate)) {
        values.createdDate = values.createdDate.map(date => moment(date).format('YYYY-MM-DD')).join(',');
      }
    }
    setPage(values);
  };

  const handleReset = () => {
    setPage({});
    loadData();
  };

  const handleViewDetail = async (item: IDepartment) => {
    try {
      // TODO: Implement delete functionality
      // toastService.success('Xóa thành công');
    } catch (error) {
      // toastService.handleError(error);
    }
  };

  const columns: any[] = [
    {
      title: 'Mã phòng ban',
      dataIndex: 'code',
      key: 'code',
      width: 90,
    },
    {
      title: 'Tên phòng ban',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description',
      width: 150,
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 150,
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (value: string) => moment(value).format('DD/MM/YYYY'),
    },
    {
      title: 'Ngày cập nhật',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 150,
      render: (value: string) => moment(value).format('DD/MM/YYYY'),
    },
    {
      title: 'Thao tác',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record: any) => (
        <>
          <BaseButton
            danger
            type="primary"
            shape="circle"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          />
        </>
      ),
    },
  ];

  const filterFields = [
    {
      key: 'code',
      name: 'Mã phòng ban',
      type: 'input',
    },
    {
      key: 'name',
      name: 'Tên phòng ban',
      type: 'input',
    },
    {
      key: 'status',
      name: 'Trạng thái',
      type: 'select',
      selectOptions: [
        { value: 'ACTIVE', name: 'Hoạt động' },
        { value: 'INACTIVE', name: 'Ngừng hoạt động' },
      ],
    },
    {
      key: 'createdDate',
      name: 'Ngày tạo',
      type: 'dateRange',
    },
  ];

  return (
    <BaseCard title="Danh sách phòng ban">
      <Row gutter={16}>
        <Col span={24}>
          <BaseFilter
            onFilter={handleFilter}
            onReset={handleReset}
            filters={filterFields}
            isLoading={false}
            styles={{ marginBottom: 16 }}
          />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={data}
            total={total}
            isLoading={isLoading}
            onPageChange={handlePageChange}
          />
        </Col>
      </Row>
    </BaseCard>
  );
};
