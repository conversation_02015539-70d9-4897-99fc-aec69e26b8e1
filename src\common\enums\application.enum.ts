export namespace NSApplication {
  export enum EStatus {
    INACTIVE = 'INACTIVE',
    ACTIVE = 'ACTIVE',
  }

  export const ESTATUS_LOCALE_LABEL = {
    [EStatus.ACTIVE]: 'enum.NSApplication.EStatus.ACTIVE',
    [EStatus.INACTIVE]: 'enum.NSApplication.EStatus.INACTIVE',
  };

  export const ESTATUS_LABEL_COLOR = {
    [EStatus.ACTIVE]: 'green',
    [EStatus.INACTIVE]: 'red',
  };

  export enum ERegisterStatus {
    UNREGISTERED = 'UNREGISTERED',
    REGISTERED = 'REGISTERED',
  }

  export const EREGISTER_STATUS_LOCALE_LABEL = {
    [ERegisterStatus.UNREGISTERED]: 'enum.NSApplication.ERegisterStatus.UNREGISTERED',
    [ERegisterStatus.REGISTERED]: 'enum.NSApplication.ERegisterStatus.REGISTERED',
  };

  export const EREGISTER_STATUS_LABEL_COLOR = {
    [ERegisterStatus.UNREGISTERED]: 'red',
    [ERegisterStatus.REGISTERED]: 'green',
  };
}
