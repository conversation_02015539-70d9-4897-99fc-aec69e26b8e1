import { CheckOutlined, EditOutlined, EyeOutlined, PlusOutlined, StopOutlined } from '@ant-design/icons';
import { Button, Flex, Modal, notification, Tag, Tooltip } from 'antd';
import { useState } from 'react';

import { IContractTemplate, ICreateContractTemplate } from '~/api/setting/contract-template/types';
import BaseCard from '~/components/base-card';
import BaseFilter from '~/components/base-filter/BaseFilter';
import BaseTable from '~/components/base-table';
import BaseText from '~/components/base-text';
import UploadTemplateContractDrawer from './component/create-template-contract-drawer';
import DetailTemplateContractDrawer from './component/detail-template-contract-drawer';
import UpdateTemplateContractDrawer from './component/update-template-contract.drawer';
import useTemplateContract from './hook/use-template-contract';

export default function ContractTemplateView() {
  const {
    data,
    total,
    setFilter,
    loadData,
    createTemplate,
    isLoading,
    updateTemplate,
    inactiveTemplate,
    activeTemplate,
  } = useTemplateContract();
  const [visibleUploadTemplateDrawer, setVisibleUploadTemplateDrawer] = useState(false);
  const [visibleDetailTemplateContractDrawer, setVisibleDetailTemplateContractDrawer] = useState(false);
  const [visibleUpdateTemplateContractDrawer, setVisibleUpdateTemplateContractDrawer] = useState(false);
  const [selectTemplate, setSelectTemplate] = useState<IContractTemplate>(null);
  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({
      pageIndex: newPageIndex,
      pageSize: newPageSize,
    });
  };

  const handleCreateTemplate = async (values: ICreateContractTemplate) => {
    try {
      await createTemplate(values);
      notification.success({
        message: 'Tạo mẫu hợp đồng thành công',
      });
      loadData();
    } catch (error) {
      notification.error({
        message: 'Tạo mẫu hợp đồng thất bại',
      });
    }
  };

  const handleUpdateTemplate = async (values: IContractTemplate) => {
    try {
      await updateTemplate(values);
      notification.success({
        message: 'Cập nhật mẫu hợp đồng thành công',
      });
      setVisibleUpdateTemplateContractDrawer(false);
      loadData();
    } catch (error) {
      notification.error({
        message: error.message,
      });
    }
  };
  const handleInactiveTemplate = async (id: string) => {
    try {
      await inactiveTemplate(id);
      notification.success({
        message: 'Ngưng hoạt động mẫu hợp đồng thành công',
      });
      loadData();
    } catch (error) {
      notification.error({
        message: error.message,
      });
    }
  };
  const handleActiveTemplate = async (id: string) => {
    try {
      await activeTemplate(id);
      notification.success({
        message: 'Hoạt động mẫu hợp đồng thành công',
      });
      loadData();
    } catch (error) {
      notification.error({
        message: error.message,
      });
    }
  };

  const handleFilter = (values: any) => {
    setFilter(values);
  };

  const handleReset = () => {
    setFilter({});
    loadData();
  };
  const columns = [
    {
      title: 'Tên mẫu',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Mã hợp đồng',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (value: string) => {
        if (value === null) return <BaseText>--</BaseText>;
        return (
          <Tag color={value === 'ACTIVE' ? 'green' : 'red'}>{value === 'ACTIVE' ? 'Hoạt động' : 'Không hoạt động'}</Tag>
        );
      },
    },
    {
      title: 'Tác vụ',
      key: 'action',
      render: (_: any, record: IContractTemplate) => (
        <Flex gap={5}>
          <Tooltip title="Xem">
            <Button
              type="primary"
              icon={<EyeOutlined />}
              onClick={() => {
                setSelectTemplate(record);
                setVisibleDetailTemplateContractDrawer(true);
              }}
            />
          </Tooltip>
          <Tooltip title="Sửa">
            <Button
              type="primary"
              icon={<EditOutlined />}
              onClick={() => {
                setSelectTemplate(record);
                setVisibleUpdateTemplateContractDrawer(true);
              }}
            />
          </Tooltip>
          {record.status === 'ACTIVE' && (
            <Tooltip title="Ngưng hoạt động">
              <Button
                danger
                icon={<StopOutlined />}
                onClick={() => {
                  Modal.confirm({
                    title: 'Xác nhận ngưng hoạt động',
                    content: 'Bạn có chắc chắn muốn ngưng hoạt động mẫu hợp đồng này?',
                    okText: 'Xác nhận',
                    cancelText: 'Hủy',
                    onOk: () => handleInactiveTemplate(record.id),
                  });
                }}
              />
            </Tooltip>
          )}
          {record.status === 'INACTIVE' && (
            <Tooltip title="Hoạt động">
              <Button
                color="green"
                type="default"
                icon={<CheckOutlined />}
                onClick={() => {
                  handleActiveTemplate(record.id);
                }}
              />
            </Tooltip>
          )}
        </Flex>
      ),
    },
  ];
  const baseFilters = [
    {
      key: 'code',
      name: 'Mã mẫu',
      type: 'input',
    },
    {
      key: 'name',
      name: 'Tên mẫu',
      type: 'input',
    },
    {
      key: 'status',
      name: 'Trạng thái',
      type: 'select',
      options: [
        {
          label: 'Hoạt động',
          value: 'ACTIVE',
        },
        {
          label: 'Không hoạt động',
          value: 'INACTIVE',
        },
      ],
    },
  ];
  return (
    <BaseCard
      title="Mẫu hợp đồng"
      buttons={[
        {
          text: 'Tạo mới mẫu hợp đồng',
          type: 'primary',
          icon: <PlusOutlined />,
          onClick: () => setVisibleUploadTemplateDrawer(true),
        },
      ]}
    >
      <UploadTemplateContractDrawer
        open={visibleUploadTemplateDrawer}
        onClose={() => setVisibleUploadTemplateDrawer(false)}
        onSubmit={async (values: any) => {
          handleCreateTemplate(values);
          setVisibleUploadTemplateDrawer(false);
        }}
      />
      <DetailTemplateContractDrawer
        open={visibleDetailTemplateContractDrawer}
        onClose={() => setVisibleDetailTemplateContractDrawer(false)}
        data={selectTemplate}
      />
      <UpdateTemplateContractDrawer
        open={visibleUpdateTemplateContractDrawer}
        onClose={() => setVisibleUpdateTemplateContractDrawer(false)}
        data={selectTemplate}
        onSubmit={handleUpdateTemplate}
      />
      <BaseFilter isLoading={false} filters={baseFilters} onFilter={handleFilter} onReset={handleReset} />
      <BaseTable
        columns={columns}
        data={data}
        total={total}
        isLoading={isLoading}
        onPageChange={handlePageChange}
        scroll={{ x: 1800 }}
      />
    </BaseCard>
  );
}
