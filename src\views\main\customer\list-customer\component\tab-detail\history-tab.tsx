import { Col, Row } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { formatDate } from '~/common/helpers/helper';

import BaseTable from '~/components/base-table';
import BaseView from '~/components/base-view';

interface IFilterLoyalty {
  programName: string;
  phoneNumber: string;
  customerCode: string;
  rewardExchange: string;
  productName: string;
  version: string;
  status: string;
  pageIndex: number;
  pageSize: number;
}

interface IOperationHistory {
  id: string;
  operationType: 'CREATE' | 'UPDATE' | 'DELETE' | 'VIEW';
  operationName: string;
  operator: string;
  operatorRole: string;
  targetObject: string;
  oldValue?: string;
  newValue?: string;
  operationTime: string;
  ipAddress: string;
  deviceInfo: string;
  status: 'SUCCESS' | 'FAILED';
  note?: string;
}

const fakeData: IOperationHistory[] = [
  {
    id: '1',
    operationType: 'CREATE',
    operationName: 'Tạo khách hàng mới',
    operator: '<PERSON>uy<PERSON><PERSON>n <PERSON>',
    operatorRole: 'Admin',
    targetObject: 'Customer',
    newValue: '{"name": "Trần Văn B", "phone": "0987654321"}',
    operationTime: '2024-03-20T10:30:00',
    ipAddress: '***********',
    deviceInfo: 'Chrome/Windows',
    status: 'SUCCESS',
    note: 'Tạo khách hàng thành công',
  },
  {
    id: '2',
    operationType: 'UPDATE',
    operationName: 'Cập nhật thông tin khách hàng',
    operator: 'Lê Thị C',
    operatorRole: 'Staff',
    targetObject: 'Customer',
    oldValue: '{"address": "Hà Nội"}',
    newValue: '{"address": "Hồ Chí Minh"}',
    operationTime: '2024-03-20T11:15:00',
    ipAddress: '***********',
    deviceInfo: 'Safari/MacOS',
    status: 'SUCCESS',
    note: 'Cập nhật địa chỉ',
  },
  {
    id: '3',
    operationType: 'DELETE',
    operationName: 'Xóa khách hàng',
    operator: 'Phạm Văn D',
    operatorRole: 'Manager',
    targetObject: 'Customer',
    oldValue: '{"id": "CUS123", "name": "Nguyễn Văn E"}',
    operationTime: '2024-03-20T14:20:00',
    ipAddress: '***********',
    deviceInfo: 'Firefox/Windows',
    status: 'FAILED',
    note: 'Không có quyền xóa',
  },
];

export const HistoryTab = () => {
  const columns: ColumnsType<IOperationHistory> = [
    {
      title: 'STT',
      key: 'stt',
      width: 50,
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Thời gian',
      dataIndex: 'operationTime',
      key: 'operationTime',
      width: 120,
      render: (value: string) => formatDate(value, 'DD/MM/YYYY HH:mm'),
    },

    {
      title: 'Người thao tác',
      dataIndex: 'operator',
      key: 'operator',
      width: 120,
    },
    {
      title: 'Mô tả thao tác',
      dataIndex: 'operatorRole',
      key: 'operatorRole',
      width: 150,
    },
    {
      title: 'Ghi chú',
      dataIndex: 'note',
      key: 'note',
      width: 200,
      render: (value: string) => value || '-',
    },
  ];

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable columns={columns} data={fakeData} total={fakeData.length} isLoading={false} scroll={{ x: 800 }} />
        </Col>
      </Row>
    </BaseView>
  );
};
