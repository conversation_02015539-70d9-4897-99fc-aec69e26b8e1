import { ExportOutlined, ImportOutlined, SaveOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Card, Checkbox, Col, Form, Input, Radio, Row, Select, Space, Table, Tabs } from 'antd';
import { useLocation } from 'react-router';

const { Option } = Select;
const { TabPane } = Tabs;

interface IProps {
  data?: any;
}

export const TargetEditComponent = (props: IProps) => {
  const location = useLocation(); // Lấy thông tin từ url khi useNavigate truyền qua
  const params = new URLSearchParams(location.search);
  const [form] = Form.useForm();

  const columns = [
    { title: 'STT', dataIndex: 'stt', key: 'stt' },
    { title: 'Mã CRM', dataIndex: 'maCRM', key: 'maCRM' },
    { title: 'Mã SAP', dataIndex: 'maSAP', key: 'maSAP' },
    { title: 'Tên', dataIndex: 'ten', key: 'ten' },
    { title: '<PERSON><PERSON> điện thoại', dataIndex: 'sdt', key: 'sdt' },
    { title: 'Email', dataIndex: 'email', key: 'email' },
    { title: 'Chức năng', dataIndex: 'chucNang', key: 'chucNang' },
  ];

  return (
    <Card title="Sửa Nhóm Mục Tiêu" bordered={false}>
      <Form layout="vertical" form={form}>
        <Row gutter={16}>
          <Col span={6}>
            <Form.Item label="Mã nhóm" name="maNhom">
              <Input defaultValue="1143" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Tên nhóm" name="tenNhom">
              <Input defaultValue="KH.NGÀY 06.06.2025.ALL DATA" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="Trạng thái" name="trangThai">
              <Radio.Group defaultValue="dangSuDung">
                <Radio value="dangSuDung">Đang sử dụng</Radio>
                <Radio value="ngungSuDung">Ngừng sử dụng</Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>

        <Tabs defaultActiveKey="1">
          <TabPane tab="Khách hàng" key="1">
            <Row gutter={16} style={{ marginTop: 16 }}>
              <Checkbox>Chỉ hiển thị thành viên có email</Checkbox>
            </Row>
            <Row gutter={16} style={{ marginTop: 16 }}>
              <Checkbox>Lọc thành viên bị trùng email</Checkbox>
            </Row>
            <Row gutter={16} style={{ marginTop: 16 }}>
              <Col span={6}>
                <Input addonBefore="Mã CRM" placeholder="Mã CRM" />
              </Col>
              <Col span={6}>
                <Input addonBefore="Mã SAP" placeholder="Mã SAP" />
              </Col>
              <Col span={6}>
                <Select placeholder="NV Kinh doanh" style={{ width: '100%' }}>
                  <Option value="">Tất cả</Option>
                </Select>
              </Col>
              <Col span={6}>
                <Select placeholder="Phòng ban" style={{ width: '100%' }}>
                  <Option value="">Tất cả</Option>
                </Select>
              </Col>
            </Row>

            <Row gutter={16} style={{ marginTop: 16 }}>
              <Col span={6}>
                <Input addonBefore="Tên" placeholder="Tên" />
              </Col>
              <Col span={6}>
                <Input addonBefore="Số điện thoại" placeholder="Số điện thoại"></Input>
              </Col>
              <Col span={6}>
                <Select placeholder="Phân khúc Khách hàng" style={{ width: '100%' }}>
                  <Option value="">Tất cả</Option>
                </Select>
              </Col>
              <Col span={6}>
                <Select placeholder="Ngành nghề" style={{ width: '100%' }}>
                  <Option value="">Tất cả</Option>
                </Select>
              </Col>
            </Row>
            <Row gutter={16} style={{ marginTop: 16 }}>
              <Col span={6}>
                <Select placeholder="Khu Vực" style={{ width: '100%' }}>
                  <Option value="">Tất cả</Option>
                </Select>
              </Col>
              <Col span={6}>
                <Select placeholder="Tỉnh/Thành phố" style={{ width: '100%' }}>
                  <Option value="">Tất cả</Option>
                </Select>
              </Col>
              <Col span={6}>
                <Select placeholder="Quận/Huyện" style={{ width: '100%' }}>
                  <Option value="">Tất cả</Option>
                </Select>
              </Col>
              <Col span={6}>
                <Select placeholder="Phường/xã" style={{ width: '100%' }}>
                  <Option value="">Tất cả</Option>
                </Select>
              </Col>
            </Row>
            <Row gutter={16} style={{ marginTop: 16 }}>
              <Col span={12}>
                <Input addonBefore="Địa chỉ" placeholder="Địa chỉ" />
              </Col>
              <Col span={12}>
                <Select placeholder="Trạng thái" style={{ width: '100%' }}>
                  <Option value="">Tất cả</Option>
                </Select>
              </Col>
            </Row>

            <Row gutter={16} style={{ marginTop: 16 }}>
              <Col span={12}>
                <Select placeholder="NV Admin" style={{ width: '100%' }}>
                  <Option value="">Tất cả</Option>
                </Select>
              </Col>
              <Col span={12}>
                <Select placeholder="Phân loại KH" style={{ width: '100%' }}>
                  <Option value="">Tất cả</Option>
                </Select>
              </Col>
            </Row>

            <Row gutter={16} style={{ marginTop: 16 }}>
              <Col span={24} style={{ textAlign: 'center' }}>
                <Button type="primary" icon={<SearchOutlined />}>
                  Tìm kiếm
                </Button>
              </Col>
            </Row>

            <Table
              style={{ marginTop: 16 }}
              columns={columns}
              dataSource={[]}
              rowKey="stt"
              pagination={{ pageSize: 10 }}
              locale={{ emptyText: 'Đang xem 0 trên tổng số 0 mục' }}
            />
          </TabPane>

          <TabPane tab="Khách hàng chưa định danh (0)" key="3" />
          <TabPane tab="Thay đổi" key="4" />
        </Tabs>

        <Space style={{ marginTop: 16 }}>
          <Button type="primary" icon={<SaveOutlined />}>
            Lưu
          </Button>
          <Button icon={<ExportOutlined />}>Export</Button>
          <Button icon={<ImportOutlined />}>Import Khách hàng</Button>
          <Button icon={<ImportOutlined />}>Import KH chưa định danh</Button>
        </Space>
      </Form>
    </Card>
  );
};
