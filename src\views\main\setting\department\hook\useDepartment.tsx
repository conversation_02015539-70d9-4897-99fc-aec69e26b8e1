import { useCallback, useEffect, useState } from 'react';
import { departmentApi } from '~/api/setting/department';
import { IDepartment, IListDepartment } from '~/api/setting/department/types';

export const useDepartment = () => {
  const [data, setData] = useState<IDepartment[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState<IListDepartment>({});

  const loadData = useCallback(async () => {
    setIsLoading(true);
    const result = await departmentApi.list(page);
    setData(Array.isArray(result) ? [...result] : []);
    setTotal(result.length);
    setIsLoading(false);
  }, [page]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    data,
    setData,
    isLoading,
    setIsLoading,
    total,
    setTotal,
    page,
    setPage,
    loadData,
  };
};
