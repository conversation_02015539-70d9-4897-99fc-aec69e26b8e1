# Tại sao chọn chúng tôi? {#why}

## Vấn đề thực tế {#the-problems}

Tôi đã làm việc trong lĩnh vực phát triển front-end nhi<PERSON><PERSON> năm, chủ yếu sử dụng React để phát triển các ứng dụng đơn trang. <PERSON><PERSON>, mỗi khi bắt đầu một dự án và muốn tìm một framework phù hợp, thực sự rất khó khăn. Hầu hết các framework đều do các nhà phát triển tự học React và xây dựng, hoặc không còn được duy trì. Cộng đồng Vue.js có rất nhiều framework đa dạng, trong khi cộng đồng React có rất ít framework, giống như sa mạc. Nhưng để phát triển hệ thống quản trị, antd vẫn là lựa chọn hàng đầ<PERSON>, vì vậy sử dụng React để phát triển là lựa chọn tốt hơn.

<PERSON><PERSON><PERSON>, chúng ta cần một framework tích hợp các chức năng cơ bản như định tuyến, quản lý trạng thái, quản lý quyền, định dạng mã và các chức năng cơ bản khác, sẵn sàng sử dụng mà không cần phải xây dựng từ đầu. Vì vậy, dựa trên kinh nghiệm phát triển của tôi và tham khảo các framework xuất sắc trong cộng đồng, tôi đã phát triển react-antd-admin.

## Tại sao không chọn UmiJS? {#why-not-umijs}

> Tuyên bố: Tôi cho rằng UmiJS rất xuất sắc.

UmiJS là một framework rất xuất sắc, nhưng nó quá nặng, tự đóng gói nhiều thứ, chi phí học tập cao, không thân thiện với người mới, và khi gặp vấn đề rất khó xác định và khó sửa đổi mã. Quan trọng nhất là các gói không phải là mới nhất nên không thể tận hưởng trải nghiệm mà công nghệ mới mang lại.

## Lời cảm ơn

Cảm ơn các framework xuất sắc của cộng đồng, trong quá trình phát triển dự án này, tôi đã tham khảo chủ yếu từ các dự án sau:

1. [vben-admin](https://github.com/anncwb/vben-admin)
2. [vue-pure-admin](https://github.com/xiaoxian521/vue-pure-admin)

## Các dự án xuất sắc khác

Tôi đã tạo hai thư mục trên GitHub, thu thập các framework React và Vue xuất sắc.

### Framework React

Nhấp để xem thư mục: [React Template](https://github.com/stars/condorheroblog/lists/react-template)

### Framework Vue

Nhấp để xem thư mục: [Vue Template](https://github.com/stars/condorheroblog/lists/vue-template)
