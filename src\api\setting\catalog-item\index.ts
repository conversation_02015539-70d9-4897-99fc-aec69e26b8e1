import { rootApiConnector } from '~/connectors';
import { IPageResponse } from '../../@common';
import {
  ICatalogItem,
  IListCatalogItem,
  IUpdateCatalogItem,
  ICreateCatalogItem,
} from './types';

const ENDPOINT = {
  LIST: 'api/client/catalog-item/list',
  CREATE: 'api/client/catalog-item/create',
  DETAIL: 'api/client/catalog-item/detail',
  UPDATE: 'api/client/catalog-item/update',
  ACTIVE: 'api/client/catalog-item/active',
  INACTIVE: 'api/client/catalog-item/inactive',
};

class CatalogItemApi {
  list = (params: IListCatalogItem) => {
    return rootApiConnector.get<IPageResponse<ICatalogItem>>(ENDPOINT.LIST, params);
  };

  create = (body: ICreateCatalogItem) => {
    return rootApiConnector.post<ICatalogItem>(ENDPOINT.CREATE, body);
  };

  detail = (params: { id: string }) => {
    return rootApiConnector.get<ICatalogItem>(ENDPOINT.DETAIL, params);
  };

  update = (params: IUpdateCatalogItem) => {
    return rootApiConnector.post<IUpdateCatalogItem>(ENDPOINT.UPDATE, params);
  };

  active = (params: { id: string }) => {
    return rootApiConnector.post<ICatalogItem>(ENDPOINT.ACTIVE, params);
  };

  inactive = (params: { id: string }) => {
    return rootApiConnector.post<ICatalogItem>(ENDPOINT.INACTIVE, params);
  };
}
export const catalogItemApi = new CatalogItemApi();
