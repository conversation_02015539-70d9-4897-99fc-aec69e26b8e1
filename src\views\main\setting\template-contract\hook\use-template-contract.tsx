import { useCallback, useEffect, useState } from 'react';
import { contractTemplateApi } from '~/api/setting/contract-template';
import {
  IContractTemplate,
  ICreateContractTemplate,
  IPaginationContractTemplate,
} from '~/api/setting/contract-template/types';

const useTemplateContract = () => {
  const [data, setData] = useState<IContractTemplate[]>([]);
  const [detailData, setDetailData] = useState<IContractTemplate | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [filter, setFilter] = useState<IPaginationContractTemplate>({});

  const detailTemplate = useCallback(
    async (id: string) => {
      setIsLoading(true);
      const result = await contractTemplateApi.detailTemplate(id);
      setDetailData(result as unknown as IContractTemplate);
      setIsLoading(false);
    },
    [detailData],
  );

  const loadData = useCallback(async () => {
    setIsLoading(true);
    const result = await contractTemplateApi.pagination(filter);
    setData(result.data as unknown as IContractTemplate[]);
    setTotal(result.total);
    setIsLoading(false);
  }, [filter]);

  const createTemplate = useCallback(async (values: ICreateContractTemplate) => {
    return await contractTemplateApi.create(values);
  }, []);

  const updateTemplate = useCallback(async (values: IContractTemplate) => {
    return await contractTemplateApi.update(values);
  }, []);

  const inactiveTemplate = useCallback(async (id: string) => {
    return await contractTemplateApi.inactive(id);
  }, []);

  const activeTemplate = useCallback(async (id: string) => {
    return await contractTemplateApi.active(id);
  }, []);

  useEffect(() => {
    loadData();
  }, [loadData]);
  return {
    data,
    detailData,
    isLoading,
    total,
    filter,
    setFilter,
    loadData,
    createTemplate,
    updateTemplate,
    inactiveTemplate,
    activeTemplate,
    detailTemplate,
  };
};
export default useTemplateContract;
