import { useDebounce } from 'ahooks';
import { ColumnType } from 'antd/es/table';
import { useEffect, useState } from 'react';
import { ListCustomerParams } from '~/api/customer/types';
import { IQuotationList } from '~/api/quotation/types';
import { enumData } from '~/common/enums/enumData';
import { QuoteStatus, QuoteStatusOptions } from '~/common/enums/quote.enum';
import helper from '~/common/helpers/helper';
import useCustomer from '~/views/main/customer/Hooks/useCustomer';

export const useFilter = () => {
  const [filterData, setFilterData] = useState<IQuotationList>({});
  const [customerData, setCustomerData] = useState([]);
  const { listCustomer } = useCustomer();

  const loadCustomer = (params: ListCustomerParams) => {
    listCustomer({ ...params, pageSize: 20, pageIndex: 1 }).then(res => {
      setCustomerData(res.data);
    });
  };

  const initData = async () => {
    await Promise.all([loadCustomer({})]);
  };

  //debounce
  const searchCustomer = (value: string) => {
    helper.debounceInput(() => {
      loadCustomer({ name: value });
    }, 300);
  };

  useEffect(() => {
    initData();
  }, []);

  // Xử lý reset bộ lọc
  const handleFilterReset = () => {
    setFilterData({});
  };

  const handleFilter = (values: any) => {
    setFilterData(values);
  };

  const filterFields = [
    {
      key: 'quotationNumber',
      name: 'Số báo giá',
      type: enumData.FILTER_TYPE.INPUT.key,
      placeholder: 'Nhập số báo giá',
    },
    {
      key: 'customerName',
      name: 'Khách hàng',
      type: enumData.FILTER_TYPE.SELECT_SEARCH.key,
      selectOptions: customerData.map(customer => ({ value: customer.id, name: customer.name })),
      placeholder: 'Nhập tên khách hàng',
      onSearch: searchCustomer,
    },
    {
      key: 'status',
      name: 'Trạng thái',
      type: enumData.FILTER_TYPE.SELECT.key,
      placeholder: 'Chọn trạng thái',
      selectOptions: Object.keys(QuoteStatusOptions).map(key => ({
        value: key,
        name: QuoteStatusOptions[key as QuoteStatus]?.label,
        label: QuoteStatusOptions[key as QuoteStatus]?.label,
      })),
    },
    {
      key: 'quotationDate',
      name: 'Ngày báo giá',
      type: enumData.FILTER_TYPE.DATE_RANGE.key,
      placeholder: 'Chọn ngày báo giá',
    },
  ];

  return {
    filterFields,
    filterData,
    setFilterData,
    handleFilter,
    handleFilterReset,
  };
};
