import {
  AppstoreOutlined,
  BankOutlined,
  BarcodeOutlined,
  CalendarOutlined,
  EnvironmentOutlined,
  FileTextOutlined,
  GlobalOutlined,
  HomeOutlined,
  MailOutlined,
  PhoneOutlined,
  PrinterOutlined,
  TagOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Card, Col, Descriptions, Row, Space, Table, Tag } from 'antd';
import { FC } from 'react';
import { NSCustomer } from '~/common/enums/customer.enum';

import BaseText from '~/components/base-text';

interface IProps {
  data: any;
}
export const InformationTab: FC<IProps> = ({ data }) => {
  // Mock data cho UI

  const columns = [
    {
      title: 'Tên',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Số điện thoại',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: 'Chức vụ',
      dataIndex: 'position',
      key: 'position',
    },
  ];

  return (
    <div>
      <Card
        title={
          <Space>
            <AppstoreOutlined style={{ color: '#1890ff' }} />
            <BaseText>Thông tin khách hàng</BaseText>
          </Space>
        }
        style={{ marginBottom: '16px', marginTop: '16px' }}
        size="small"
      >
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12}>
            <Descriptions column={1}>
              <Descriptions.Item
                label={
                  <Space>
                    <BarcodeOutlined />
                    <span>Mã khách hàng</span>
                  </Space>
                }
              >
                {data?.code}
              </Descriptions.Item>

              <Descriptions.Item
                label={
                  <Space>
                    <UserOutlined />
                    <span>Tên khách hàng</span>
                  </Space>
                }
              >
                {data?.name}
              </Descriptions.Item>

              <Descriptions.Item
                label={
                  <Space>
                    <FileTextOutlined />
                    <span>Mã số thuế</span>
                  </Space>
                }
              >
                {data?.taxNumber}
              </Descriptions.Item>

              <Descriptions.Item
                label={
                  <Space>
                    <PhoneOutlined />
                    <span>Số điện thoại</span>
                  </Space>
                }
              >
                {data?.phone}
              </Descriptions.Item>
              <Descriptions.Item
                label={
                  <Space>
                    <CalendarOutlined />
                    <span>Ngày tạo</span>
                  </Space>
                }
              >
                {new Date(data?.createdDate).toLocaleDateString('vi-VN')}
              </Descriptions.Item>
            </Descriptions>
          </Col>
          <Col xs={24} sm={12}>
            <Descriptions column={1}>
              <Descriptions.Item
                label={
                  <Space>
                    <MailOutlined />
                    <span>Email</span>
                  </Space>
                }
              >
                {data?.email}
              </Descriptions.Item>
              <Descriptions.Item
                label={
                  <Space>
                    <PrinterOutlined />
                    <span>Fax</span>
                  </Space>
                }
              >
                {data?.fax || '--'}
              </Descriptions.Item>
              <Descriptions.Item
                label={
                  <Space>
                    <TagOutlined />
                    <span>Nguồn khách hàng</span>
                  </Space>
                }
              >
                <Tag color="purple">{data?.source}</Tag>
              </Descriptions.Item>
              <Descriptions.Item
                label={
                  <Space>
                    <GlobalOutlined />
                    <span>Thị trường</span>
                  </Space>
                }
              >
                <Tag color="blue">{NSCustomer.MARKET[data?.market]?.name}</Tag>
              </Descriptions.Item>
            </Descriptions>
          </Col>
        </Row>
      </Card>

      <Card title={<BaseText>Địa chỉ khách hàng</BaseText>} size="small" style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12}>
            <Descriptions column={1}>
              <Descriptions.Item
                label={
                  <Space>
                    <EnvironmentOutlined />
                    <span>Phường/Xã</span>
                  </Space>
                }
              >
                {data?.wareName}
              </Descriptions.Item>

              <Descriptions.Item
                label={
                  <Space>
                    <EnvironmentOutlined />
                    <span>Quận/Huyện</span>
                  </Space>
                }
              >
                {data?.districtName}
              </Descriptions.Item>
              <Descriptions.Item
                label={
                  <Space>
                    <EnvironmentOutlined />
                    <span>Tỉnh/Thành phố</span>
                  </Space>
                }
              >
                {data?.provinceName}
              </Descriptions.Item>
            </Descriptions>
          </Col>
          <Col xs={24} sm={12}>
            <Descriptions column={1}>
              <Descriptions.Item
                label={
                  <Space>
                    <HomeOutlined />
                    <span>Địa chỉ</span>
                  </Space>
                }
              >
                {data?.fullAddress}
              </Descriptions.Item>

              <Descriptions.Item
                label={
                  <Space>
                    <UserOutlined />
                    <span>Người tạo</span>
                  </Space>
                }
              >
                {data?.createdBy || '--'}
              </Descriptions.Item>
            </Descriptions>
          </Col>
        </Row>
      </Card>

      <Card
        title={
          <Space>
            <BankOutlined style={{ color: '#722ed1' }} />
            <BaseText>Danh sách nhân viên</BaseText>
          </Space>
        }
        size="small"
      >
        <Table columns={columns} dataSource={[]} pagination={false} size="small" rowKey="id" />
      </Card>
    </div>
  );
};
