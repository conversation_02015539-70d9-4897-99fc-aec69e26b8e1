import { IPageRequest } from '~/api/@common';
import { contactApi } from '~/api/contact';
import { I<PERSON>ont<PERSON>, IContactList, IContactUpdate } from '~/api/contact/types';

const useCustomerContact = () => {
  const createCustomerContact = async (data: IContact) => {
    return contactApi.create(data);
  };

  const updateCustomerContact = async (data: IContactUpdate) => {
    return contactApi.update(data);
  };

  //setActive
  const deleteCustomerContact = async (id: string) => {
    return contactApi.delete({ id });
  };

  //list
  const listCustomerContact = async (params: IContactList) => {
    return contactApi.list(params);
  };

  const detailCustomerContact = async (id: string) => {
    return contactApi.detail({ id });
  };

  return {
    createCustomerContact,
    updateCustomerContact,
    deleteCustomerContact,
    listCustomerContact,
    detailCustomerContact,
  };
};

export default useCustomerContact;
