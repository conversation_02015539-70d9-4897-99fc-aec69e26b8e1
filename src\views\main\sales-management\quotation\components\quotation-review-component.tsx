import { Card, Descriptions, Divider, Table, Typography } from 'antd';

const { Title, Text } = Typography;

const serviceData = [
  {
    key: '1',
    stt: 1,
    service: 'Vận chuyển nội địa',
    description: 'TP.HCM → Hà Nội (3T)',
    unit: 'Chuyến',
    quantity: 1,
    unitPrice: 15000000,
    amount: 15000000,
  },
  {
    key: '2',
    stt: 2,
    service: '<PERSON><PERSON> bốc xếp hàng hóa',
    description: '2 container 40ft',
    unit: 'Lần',
    quantity: 1,
    unitPrice: 2000000,
    amount: 2000000,
  },
  {
    key: '3',
    stt: 3,
    service: 'Lưu kho tại depot',
    description: '3 ngày đầu miễn phí, sau đó 50k/ngày',
    unit: 'Ngày',
    quantity: 2,
    unitPrice: 50000,
    amount: 100000,
  },
  {
    key: '4',
    stt: 4,
    service: '<PERSON>ụ phí cầu đường, BOT',
    description: '<PERSON><PERSON> bao gồm trong đơn giá',
    unit: 'Gói',
    quantity: 1,
    unitPrice: 0,
    amount: 0,
  },
];

const columns = [
  {
    title: 'STT',
    dataIndex: 'stt',
    width: 60,
  },
  {
    title: 'Dịch vụ',
    dataIndex: 'service',
    render: (text: string, record: any) => (
      <>
        <Text strong>{text}</Text>
        <br />
        <Text type="secondary" italic>
          {record.description}
        </Text>
      </>
    ),
  },
  {
    title: 'ĐVT',
    dataIndex: 'unit',
    width: 80,
  },
  {
    title: 'Số lượng',
    dataIndex: 'quantity',
    width: 80,
  },
  {
    title: 'Đơn giá (VNĐ)',
    dataIndex: 'unitPrice',
    align: 'right' as const,
    render: (value: number) => value.toLocaleString(),
  },
  {
    title: 'Thành tiền (VNĐ)',
    dataIndex: 'amount',
    align: 'right' as const,
    render: (value: number) => value.toLocaleString(),
  },
];

export const QuotationReviewComponent = () => {
  const total = 17100000;
  const vat = total * 0.1;
  const grandTotal = total + vat;

  return (
    <Card title="BÁO GIÁ DỊCH VỤ LOGISTICS" bordered={false}>
      <Divider orientation="left">I. Thông tin chung</Divider>
      <Descriptions column={2} bordered size="small">
        <Descriptions.Item label="Số báo giá">BG-2025-057</Descriptions.Item>
        <Descriptions.Item label="Ngày báo giá">17/06/2025</Descriptions.Item>
        <Descriptions.Item label="Khách hàng">Công ty TNHH XYZ Việt Nam</Descriptions.Item>
        <Descriptions.Item label="Người nhận">Nguyễn Văn A – Trưởng phòng Logistics</Descriptions.Item>
        <Descriptions.Item label="Điện thoại">0903 xxx xxx</Descriptions.Item>
        <Descriptions.Item label="Email"><EMAIL></Descriptions.Item>
      </Descriptions>

      <Divider orientation="left">II. Chi tiết dịch vụ báo giá</Divider>
      <Table
        columns={columns}
        dataSource={serviceData}
        pagination={false}
        bordered
        summary={() => (
          <>
            <Table.Summary.Row>
              <Table.Summary.Cell index={0} colSpan={5} align="right">
                <Text strong>Tổng cộng (chưa VAT)</Text>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={5} align="right">
                {total.toLocaleString()}
              </Table.Summary.Cell>
            </Table.Summary.Row>
            <Table.Summary.Row>
              <Table.Summary.Cell index={0} colSpan={5} align="right">
                <Text strong>Thuế GTGT (10%)</Text>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={5} align="right">
                {vat.toLocaleString()}
              </Table.Summary.Cell>
            </Table.Summary.Row>
            <Table.Summary.Row>
              <Table.Summary.Cell index={0} colSpan={5} align="right">
                <Text strong>Tổng thanh toán</Text>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={5} align="right">
                <Text strong>{grandTotal.toLocaleString()}</Text>
              </Table.Summary.Cell>
            </Table.Summary.Row>
          </>
        )}
      />

      <Divider orientation="left">III. Điều khoản thanh toán</Divider>
      <Text>
        - Thanh toán bằng chuyển khoản trong vòng 07 ngày kể từ ngày hoàn tất giao hàng
        <br />
        - Báo giá có hiệu lực trong vòng 15 ngày kể từ ngày phát hành
        <br />
        - Giá chưa bao gồm chi phí phát sinh ngoài hợp đồng (nếu có)
        <br />- Giá đã bao gồm phí cầu đường, xăng dầu
      </Text>

      <Divider orientation="left">IV. Thông tin tài khoản nhận thanh toán</Divider>
      <Descriptions column={1} size="small" bordered>
        <Descriptions.Item label="Ngân hàng">Vietcombank – CN TP.HCM</Descriptions.Item>
        <Descriptions.Item label="Chủ tài khoản">CÔNG TY TNHH ABC LOGISTICS</Descriptions.Item>
        <Descriptions.Item label="Số tài khoản">0123 456 789</Descriptions.Item>
      </Descriptions>

      <Divider />
      <Text>
        Người lập báo giá: <strong>Nguyễn Thị B</strong>
        <br />
        0933 xxx xxx – <EMAIL>
      </Text>
    </Card>
  );
};
