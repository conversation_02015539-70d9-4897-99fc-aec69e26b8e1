import { usePreferencesStore } from '~/store';
import { cn } from '~/utils';

interface LayoutFooterProps {
  className?: string;
}
export default function LayoutFooter({ className }: LayoutFooterProps) {
  const { enableFooter, companyName, companyWebsite, copyrightDate, ICPNumber, ICPLink } = usePreferencesStore();
  if (!enableFooter) return null;

  return (
    <footer
      className={cn(
        'h-10 flex flex-wrap items-center justify-center text-xs md:text-sm text-colorTextSecondary',
        className,
      )}
    >
      {ICPNumber ? (
        <span>
          <a href={ICPLink} rel="noreferrer noopener" target="_blank">
            {ICPNumber}
          </a>
          &nbsp;
        </span>
      ) : null}
      Copyright &copy;&nbsp; 2025 &nbsp;
      <a href="https://www.apetechs.com" rel="noreferrer noopener" target="_blank">
        APETECHS &nbsp;
      </a>
      &nbsp;
      {/* {copyrightDate}
      {copyrightDate ? <>&nbsp;</> : ''}
      {companyName ? (
        <span>
          <a href={companyWebsite} rel="noreferrer noopener" target="_blank">
            {companyName}
            &nbsp;
          </a>
        </span>
      ) : null} */}
      All right reserved
    </footer>
  );
}
