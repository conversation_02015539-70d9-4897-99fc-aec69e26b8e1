import { useCallback, useEffect, useState } from 'react';
import { contractApi } from '~/api/contract';
import { IContract, ICreateContractDto, IPaginationContractDto } from '~/api/contract/types';

const useContract = () => {
  const [data, setData] = useState<IContract[]>([]);
  const [detailData, setDetailData] = useState<IContract | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [filter, setFilter] = useState<IPaginationContractDto>({} as IPaginationContractDto);

  const loadData = useCallback(async () => {
    setIsLoading(true);
    try {
      const result = await contractApi.pagination(filter);
      setData(result.data as unknown as IContract[]);
      setTotal(result.total);
    } catch (error) {
      console.error('Lỗi khi tải dữ liệu hợp đồng:', error);
    } finally {
      setIsLoading(false);
    }
  }, [filter]);

  const createContract = useCallback(async (values: ICreateContractDto) => {
    await contractApi.create(values);
  }, []);

  const updateContract = useCallback(async (values: IContract) => {
    await contractApi.update(values);
  }, []);

  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    data,
    total,
    filter,
    loadData,
    isLoading,
    createContract,
    updateContract,
    setFilter,
  };
};

export default useContract;
