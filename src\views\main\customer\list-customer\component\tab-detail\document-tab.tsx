import { Col, Row, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import BaseTable from '~/components/base-table';
import BaseView from '~/components/base-view';

interface IDocument {
  id: string;
  complaintType: string;
  fileName: string;
  createdBy: string;
  createdAt: string;
}

interface IFilterDocument {
  fileType?: string;
  fileName?: string;
  createdBy?: string;
  pageIndex: number;
  pageSize: number;
}

// Mock data for demonstration
const mockDocuments: IDocument[] = [
  {
    id: '1',
    complaintType: 'Hợp đồng mua bán',
    fileName: 'Hợp đồng mua bán số 123.pdf',
    createdBy: '<PERSON>uyễn <PERSON>ăn <PERSON>',
    createdAt: '2024-03-15T08:30:00',
  },
  {
    id: '2',
    complaintType: '<PERSON><PERSON>o cáo tài chính',
    fileName: '<PERSON>áo cáo tài chính Q1.docx',
    createdBy: 'Trần Thị B',
    createdAt: '2024-03-14T14:20:00',
  },
  {
    id: '3',
    complaintType: '<PERSON>h sách khách hàng',
    fileName: '<PERSON>h sách khách hàng.xlsx',
    createdBy: 'Lê Văn C',
    createdAt: '2024-03-13T10:15:00',
  },
  {
    id: '4',
    complaintType: 'Biên bản họp',
    fileName: 'Biên bản họp.pdf',
    createdBy: 'Phạm Thị D',
    createdAt: '2024-03-12T16:45:00',
  },
  {
    id: '5',
    complaintType: 'Hình ảnh sản phẩm',
    fileName: 'Hình ảnh sản phẩm.jpg',
    createdBy: 'Hoàng Văn E',
    createdAt: '2024-03-11T09:30:00',
  },
];

export const DocumentTab = () => {
  // Using mock data instead of API call
  const data = { data: mockDocuments, total: mockDocuments.length };
  const isLoading = false;

  const columns: ColumnsType<IDocument> = [
    {
      title: 'STT',
      key: 'stt',
      width: 60,
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Tên file',
      dataIndex: 'fileName',
      key: 'fileName',
      width: 300,
    },
    {
      title: 'Loại khiếu nại',
      dataIndex: 'complaintType',
      key: 'complaintType',
      width: 150,
      render: (value: string) => {
        return (
          <Tag color="blue" style={{ fontSize: '14px', padding: '4px 12px' }}>
            {value?.toUpperCase() || '-'}
          </Tag>
        );
      },
    },
    {
      title: 'Người tạo',
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 200,
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      align: 'center',
      render: (value: string) => (value ? new Date(value).toLocaleDateString('vi-VN') : '-'),
    },
  ];

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={data?.data || []}
            total={data?.total || 0}
            isLoading={isLoading}
            scroll={{ x: 1000 }}
          />
        </Col>
      </Row>
    </BaseView>
  );
};
