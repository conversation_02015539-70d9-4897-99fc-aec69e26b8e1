import { PlusOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Card, Space } from 'antd';
import React from 'react';

interface ButtonConfig {
  text: string;
  icon?: React.ReactNode;
  onClick?: () => void;
  type?: 'primary' | 'default' | 'dashed' | 'link' | 'text';
  danger?: boolean;
}

interface BaseCardProps {
  title: React.ReactNode;
  buttons?: ButtonConfig[];
  hidden?: boolean;
  children?: React.ReactNode;
  extra?: React.ReactNode;
  // Giữ lại các props cũ để tương thích ngược
  buttonText?: string;
  onButtonClick?: () => void;
}

const BaseCard: React.FC<BaseCardProps> = ({
  title,
  buttons = [],
  buttonText,
  onButtonClick,
  hidden = false,
  children,
  extra,
  ...rest
}) => {
  // Tạo danh sách buttons từ cả hai cách (cũ và mới)
  const allButtons = [...buttons];

  // Thêm button từ cách cũ nếu có
  if (buttonText) {
    allButtons.push({
      text: buttonText,
      icon: <PlusOutlined />,
      onClick: onButtonClick,
      type: 'primary',
    });
  }

  return (
    <Card
      className="m-2"
      {...rest}
      title={title}
      extra={
        !hidden && (
          <Space>
            {extra}
            {allButtons.map((btn, index) => (
              <Button
                className="mr-2"
                key={index}
                type={btn.type || 'primary'}
                icon={btn.icon}
                onClick={btn.onClick}
                danger={btn.danger}
              >
                {btn.text}
              </Button>
            ))}
          </Space>
        )
      }
    >
      {children}
    </Card>
  );
};

export default BaseCard;
