import { EditOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { Space, Tabs } from 'antd';
import { useState } from 'react';
import BaseButton from '~/components/base-button';
import BaseModal from '~/components/base-modal';
import { CustomerAddressTab } from './tab-edit/CustomerAddressTab';
import { CustomerContactInfoTab } from './tab-edit/CustomerContactInfoTab';
import { CustomerInfoTab } from './tab-edit/CustomerInfoTab';
import { ICustomer } from '~/api/customer/types';
import { useKeepAliveRef } from 'keepalive-for-react';
import useCustomer from '../../Hooks/useCustomer';

interface IProps {
  data: ICustomer;
  onClose?: () => void;
}
const EditButton = ({ data, onClose }: IProps) => {
  // Sử dụng useState thay vì useModal
  const [open, setOpen] = useState(false);
  // Hàm mở modal
  const openModal = () => {
    setOpen(true);
  };

  // Hàm đóng modal
  const closeModal = () => {
    setOpen(false);
    onClose();
  };

  const modalContent = (
    <div>
      <Tabs
        defaultActiveKey="1"
        items={[
          {
            key: '1',
            label: (
              <Space>
                <InfoCircleOutlined />
                <span>Thông tin chung</span>
              </Space>
            ),
            children: <CustomerInfoTab onClose={closeModal} data={data} />,
          },
          {
            key: '2',
            label: (
              <Space>
                <InfoCircleOutlined />
                <span>Thông tin địa chỉ</span>
              </Space>
            ),
            children: <CustomerAddressTab onClose={closeModal} customerData={data} />,
          },
          {
            key: '3',
            label: (
              <Space>
                <InfoCircleOutlined />
                <span>Thông tin liên hệ</span>
              </Space>
            ),
            children: <CustomerContactInfoTab onClose={closeModal} customerData={data} />,
          },
        ]}
      />
    </div>
  );

  return (
    <>
      <BaseButton icon={<EditOutlined />} onClick={openModal} type="primary" tooltip="Chỉnh sửa" />
      <BaseModal
        open={open}
        onClose={closeModal}
        title="Chỉnh sửa khách hàng"
        description="Chỉnh sửa thông tin khách hàng"
        childrenBody={modalContent}
      />
    </>
  );
};

export default EditButton;
