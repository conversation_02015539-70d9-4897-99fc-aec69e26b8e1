---
layout: home

hero:
  name: "React Antd Admin"
  text: "<PERSON><PERSON><PERSON> hệ thống quản lý doanh nghiệp"
  tagline: <PERSON><PERSON><PERSON> khung quản trị doanh nghiệp dựa trên React, Vite, Ant Design và TypeScript
  actions:
    - theme: brand
      text: Bắt đầu
      link: /vi/guide/what-is-react-antd-admin
    - theme: alt
      text: Xem trên GitHub
      link: https://github.com/condorheroblog/react-antd-admin

features:
  - title: Công nghệ hiện đại
    details: Sử dụng React 18, Vite, TypeScript và Ant Design 5 để xây dựng ứng dụng hiện đại và hiệu quả.
  - title: Quản lý trạng thái đơn giản
    details: Sử dụng Zustand để quản lý trạng thái một cách đơn giản và hiệu quả.
  - title: <PERSON>a ngôn ngữ
    details: Hỗ trợ đa ngôn ngữ với i18next, bao gồm tiế<PERSON>, tiế<PERSON> Trung và tiếng Việt.
  - title: <PERSON><PERSON><PERSON> soát truy cập
    details: <PERSON><PERSON> thống kiểm soát truy cập dựa trên vai trò và quyền, dễ dàng tùy chỉnh.
  - title: Giao diện linh hoạt
    details: Nhiều bố cục và tùy chọn giao diện, bao gồm chế độ sáng và tối.
  - title: Định tuyến mạnh mẽ
    details: Hệ thống định tuyến dựa trên React Router v6 với hỗ trợ cho các tuyến lồng nhau và bảo vệ tuyến.
---
