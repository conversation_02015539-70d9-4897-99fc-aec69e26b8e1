import type { AppRouteRecordRaw } from '~/router/types';

import { $t } from '~/locales';

import { lazy } from 'react';
import { APE_CALLBACK_PATH, LOGIN_PATH } from '../core.path';

const Login = lazy(() => import('~/views/@core/auth/login'));
const ApeCallback = lazy(() => import('~/views/@core/auth/login/containers/sso-login/ApeCallback'));

const routes: AppRouteRecordRaw[] = [
  {
    path: LOGIN_PATH,
    Component: Login,
    handle: {
      hideInMenu: true,
      title: $t('authority.login'),
    },
  },
  {
    path: APE_CALLBACK_PATH,
    Component: ApeCallback,
    handle: {
      hideInMenu: true,
      title: $t('authority.login.callback'),
    },
  },
];

export default routes;
