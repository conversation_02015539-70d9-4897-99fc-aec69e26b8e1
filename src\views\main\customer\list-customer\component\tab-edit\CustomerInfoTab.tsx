import { CloseOutlined, SaveOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Card, Col, Form, Input, message, notification, Row, Select } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useEffect, useState } from 'react';
import useCustomer from '../../../Hooks/useCustomer';
import { ICustomer } from '~/api/customer/types';
import { ECustomer, NSCustomer } from '~/common/enums/customer.enum';
import helper from '~/common/helpers/helper';
import { useEmployee } from '~/views/main/setting/employee/hook/useEmployee';
import { IEmployee } from '~/api/setting/employee/types';

type IProps = {
  onClose: () => void;
  data: ICustomer;
};

export const CustomerInfoTab = (props: IProps) => {
  const { onClose } = props;
  const [form] = useForm();
  const [isUpdatingCustomer, setIsUpdatingCustomer] = useState(false);

  const { updateCustomer } = useCustomer();
  const { data } = props;

  useEffect(() => {
    form.setFieldsValue(data);
  }, [data]);

  const handleSave = async (values: any) => {
    if (!values) return;
    // Giả lập việc cập nhật khách hàng
    setIsUpdatingCustomer(true);

    const updateData = {
      ...data,
      ...values,
    };
    updateCustomer(updateData)
      .then(res => {
        if (res) {
          notification.success({
            message: 'Cập nhật thông tin khách hàng thành công',
            placement: 'top',
          });
          setIsUpdatingCustomer(false);
          onClose();
        }
      })
      .catch(err => {
        notification.error({
          message: err.message,
          placement: 'top',
        });
        setIsUpdatingCustomer(false);
      });
  };
  // Mock data cho UI
  const [employees, setEmployees] = useState<IEmployee[]>([]);

  const { data: employeesData, setPage } = useEmployee();

  useEffect(() => {
    setPage({
      pageIndex: 1,
      pageSize: 20,
    });
  }, []);

  useEffect(() => {
    setEmployees(employeesData);
  }, [employeesData]);

  return (
    <div>
      <Form form={form} layout="vertical" onFinish={handleSave}>
        <Card title="Thông tin chung">
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item
                label="Tên khách hàng"
                name="name"
                rules={[{ required: true, message: 'Vui lòng nhập tên khách hàng (Tối đa 255 ký tự)', max: 255 }]}
              >
                <Input placeholder="Nhập Tên khách hàng" maxLength={255} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label="Mã số thuế"
                name="taxNumber"
                rules={[{ required: true, message: 'Vui lòng nhập mã số thuế (9-20 ký tự)', min: 9, max: 20 }]}
              >
                <Input placeholder="Nhập Mã số thuế" maxLength={20} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label="Số điện thoại"
                name="phone"
                rules={[
                  { required: true, message: 'Vui lòng nhập số điện thoại (9-11 số)', min: 9, max: 11 },
                  { pattern: /^[0-9]*$/, message: 'Số điện thoại chỉ được chứa số' },
                ]}
              >
                <Input placeholder="Nhập Số điện thoại" maxLength={11} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label="Email"
                name="email"
                rules={[
                  {
                    required: true,
                    message: 'Vui lòng nhập email',
                  },
                  {
                    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                    message: 'Email không hợp lệ',
                  },
                  { max: 255, message: 'Email không được vượt quá 255 ký tự' },
                ]}
              >
                <Input placeholder="Nhập Email" maxLength={255} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="Fax" name="fax" rules={[{ max: 30, message: 'Fax không được vượt quá 30 ký tự' }]}>
                <Input placeholder="Nhập Fax" maxLength={30} />
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item label="Nguồn khách hàng" name="source">
                <Select placeholder="-- Vui lòng chọn --">
                  {helper.convertObjToArray(ECustomer.ECustomerSource).map(item => (
                    <Select.Option value={item.value}>{item.name}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label="Thị trường"
                name="market"
                rules={[{ required: true, message: 'Vui lòng chọn thị trường' }]}
              >
                <Select placeholder="-- Vui lòng chọn --">
                  {helper.convertObjToArray(NSCustomer.MARKET).map(item => (
                    <Select.Option value={item.value}>{item.name}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Nhân viên phụ trách"
                name="salesRep"
                rules={[{ required: true, message: 'Vui lòng chọn nhân viên phụ trách' }]}
              >
                <Select mode="multiple" maxCount={1} placeholder="-- Vui lòng chọn --">
                  {employees?.map(item => (
                    <Select.Option key={item.id} value={item.id}>
                      {/* {item.code || ''} - {item.fullName} - {item?.positionName || ''} - {item?.departmentName || ''} */}
                      {item.fullName}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <div
            style={{
              textAlign: 'center',
              marginTop: 24,
              paddingTop: 16,
            }}
          >
            <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={isUpdatingCustomer}>
              Lưu thông tin chung
            </Button>
          </div>
        </Card>
      </Form>
      <div
        style={{
          textAlign: 'right',
          marginTop: 24,
          borderTop: '1px solid #f0f0f0',
          paddingTop: 16,
        }}
      >
        <Button
          danger
          htmlType="button"
          icon={<CloseOutlined style={{ fontSize: 12 }} />}
          style={{ marginRight: 8 }}
          onClick={onClose}
        >
          Đóng
        </Button>
      </div>
    </div>
  );
};
