import { IPageRequest, IPageResponse } from '../../@common/types';

export enum EDepartmentStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export interface IListDepartment extends IPageRequest {
  code?: string;
  name?: string;
  status?: EDepartmentStatus;
  createdDate?: string;
}

export interface IDepartment {
  id: string;
  createdAt: string;
  updatedAt: string;
}

export interface IListDepartmentResponse extends IPageResponse<IDepartment> {}
