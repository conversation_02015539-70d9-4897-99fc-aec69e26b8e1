import { IPageRequest, IPageResponse } from '../../@common/types';

export enum ECatalogStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  DRAFT = 'DRAFT',
}

export interface ICreateCatalog {
  code: string;
  name: string;
  description?: string | null;
  images?: string | null;
  attachments?: string | null;
  status: ECatalogStatus;
}

export interface IUpdateCatalog extends Partial<ICreateCatalog> {
  id: string;
}

export interface IListCatalog extends IPageRequest {
  code?: string;
  name?: string;
  status?: ECatalogStatus;
  createdFrom?: string;
  createdTo?: string;
  createdDate?: string[];
}

export interface ICatalog extends ICreateCatalog {
  id: string;
  createdAt: string;
  updatedAt: string;
}

export interface IListCatalogResponse extends IPageResponse<ICatalog> {}
