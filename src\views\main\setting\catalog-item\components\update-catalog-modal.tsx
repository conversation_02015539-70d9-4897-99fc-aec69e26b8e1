import { SaveOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Col, Drawer, Form, Input, InputNumber, Row, Select, notification } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useEffect, useState } from 'react';
import { NSCatalog } from '~/common/enums/catalog.enum';
import { useCatalogItem } from '../hook/useCatalogItem';
import { Space } from 'antd/lib';
import CatalogSelect from '~/components/catalog-select';

const { TextArea } = Input;

interface UpdateItemModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  item: any;
}

const UpdateCatalogItemModal = ({ open, onClose, onSuccess, item }: UpdateItemModalProps) => {
  const [form] = useForm();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { update } = useCatalogItem();

  useEffect(() => {
    form.resetFields();
    if (open) {
      form.setFieldsValue(item);
    }
  }, [open]);

  const handleUpdate = async (values: any) => {
    if (!values) return;

    setIsSubmitting(true);

    try {
      await update({ ...values, id: item.id });
      setIsSubmitting(false);
      onClose();
      if (onSuccess) onSuccess();
      notification.success({
        message: 'Thành công',
        description: 'Cập nhật thành công',
        placement: 'top',
      });
    } catch (error) {
      setIsSubmitting(false);
      notification.error({
        message: 'Lỗi',
        description: 'Cập nhật thất bại',
        placement: 'top',
      });
    }
  };

  const modalContent = (
    <Form form={form} layout="vertical" onFinish={handleUpdate}>
      <Row gutter={16}>
        {/* Danh mục */}
        <Col span={24}>
          <Form.Item label="Danh mục" name="catalogId" rules={[{ required: true, message: 'Vui lòng chọn danh mục' }]}>
            <CatalogSelect onChange={value => form.setFieldValue('catalogId', value)} value={item.catalogId} />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            label="Mã sản phẩm"
            name="code"
            rules={[
              { required: true, message: 'Vui lòng nhập mã danh mục' },
              {
                pattern: /^\w+$/,
                message: 'Mã danh mục chỉ được chứa chữ cái, số và dấu gạch dưới',
              },
            ]}
          >
            <Input placeholder="Nhập mã sản phẩm" />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            label="Tên sản phẩm"
            name="name"
            rules={[{ required: true, message: 'Vui lòng nhập tên sản phẩm' }]}
          >
            <Input placeholder="Nhập tên sản phẩm" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Đơn vị tính" name="unit">
            <Select placeholder="Chọn đơn vị tính">
              {Object.values(NSCatalog.EUnit).map(item => (
                <Select.Option key={item.code} value={item.code}>
                  {item.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item label="Đơn giá" name="unitPrice">
            <InputNumber
              min={0}
              formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              style={{ width: '100%' }}
              placeholder="Nhập đơn giá"
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item label="Mô tả" name="description">
            <TextArea rows={4} placeholder="Nhập mô tả" />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );

  return (
    <Drawer
      title="Cập nhật sản phẩm / dịch vụ"
      width={720}
      onClose={onClose}
      open={open}
      extra={
        <Space>
          <Button onClick={onClose}>Hủy</Button>
          <Button onClick={() => form.submit()} type="primary" icon={<SaveOutlined />} loading={isSubmitting}>
            Cập nhật
          </Button>
        </Space>
      }
    >
      {modalContent}
    </Drawer>
  );
};

export default UpdateCatalogItemModal;
