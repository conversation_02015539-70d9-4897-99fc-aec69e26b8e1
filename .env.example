# API Base URL
VITE_ROOT_API_BASE_URL = "http://localhost:4501"

# Default Home Path
VITE_BASE_HOME_PATH = "/home"

# Web Title
VITE_GLOB_APP_TITLE = "Ape Auth Tenant"

# React Router Mode
# VITE_ROUTER_MODE = hash


# APE SSO CONFIG
VITE_SSO_CLIENT_ID = "TENNANT_SETTING_4bad9009a4924985917d92420945a745"
VITE_SSO_CLIENT_SECRET = "a0ba66d24c12d5f6cddfdadf796c0dc30a4d408a881de44a883c8004dabaa601"
VITE_SSO_REDIRECT_URI = "http://localhost:4001/api/client/auth/ape/callback"
