import { customerApi } from '~/api/customer';

const useCustomer = () => {
  const listCustomer = filterData => {
    return customerApi.list(filterData);
  };

  const createCustomer = data => {
    return customerApi.create(data);
  };

  const updateCustomer = data => {
    return customerApi.update(data);
  };

  //setActiv
  const setActiveCustomer = (id: string) => {
    return customerApi.setActive({ id });
  };

  //detail
  const detailCustomer = (id: string) => {
    return customerApi.detail({ id });
  };

  return {
    listCustomer,
    createCustomer,
    updateCustomer,
    setActiveCustomer,
    detailCustomer,
  };
};

export default useCustomer;
