import { DeleteOutlined, EditOutlined, PlusOutlined, SaveOutlined } from '@ant-design/icons';
import { But<PERSON>, Card, Checkbox, Col, Divider, Drawer, Form, Input, notification, Row, Select } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import BaseModal from '~/components/base-modal';
import BaseTable from '~/components/base-table';

import { ECustomer, NSCustomer } from '~/common/enums/customer.enum';
import { ModalContact } from './modal/modal-contact';
import useCustomer from '../../Hooks/useCustomer';
import useCustomerContact from '../../Hooks/useCustomerContact';
import { useEmployee } from '~/views/main/setting/employee/hook/useEmployee';
import { IEmployee } from '~/api/setting/employee/types';
import helper from '~/common/helpers/helper';
import dayjs from 'dayjs';
import { render } from 'nprogress';

interface CreateCustomerModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export interface ContactInfo {
  id?: string;
  name: string;
  position?: NSCustomer.Position;
  isDecisionMaker?: boolean;
  email?: string;
  phone?: string;
  note?: string;
}

const CreateCustomerModal = ({ open, onClose, onSuccess }: CreateCustomerModalProps) => {
  const [isCreatingCustomer, setIsCreatingCustomer] = useState(false);
  const [form] = useForm();

  // Mock data cho UI
  const [employees, setEmployees] = useState<IEmployee[]>([]);

  const { data: employeesData, setPage } = useEmployee();

  // State cho thông tin liên hệ
  const [contacts, setContacts] = useState<ContactInfo[]>([]);
  const [contactModalOpen, setContactModalOpen] = useState(false);
  const [editingContact, setEditingContact] = useState<any>(null);

  const { createCustomer } = useCustomer();

  const handleSave = async (values: any) => {
    setIsCreatingCustomer(true);
    const formattedValues = {
      ...values,
      contacts: contacts.length > 0 ? contacts : undefined,
    };
    await createCustomer(formattedValues)
      .then(res => {
        if (res) {
          notification.success({
            message: res.message,
            placement: 'top',
          });
          setIsCreatingCustomer(false);
          onClose();
          if (onSuccess) onSuccess();
        }
      })
      .catch(err => {
        notification.error({
          message: 'Có lỗi xảy ra',
          description: err.message,
          placement: 'top',
        });
        setIsCreatingCustomer(false);
      });
    setIsCreatingCustomer(false);
  };

  useEffect(() => {
    setPage({
      pageIndex: 1,
      pageSize: 20,
    });
  }, []);

  useEffect(() => {
    setEmployees(employeesData);
  }, [employeesData]);

  // Thêm mới contact
  const handleAddContact = () => {
    setContacts(prev => [...prev, { id: dayjs().toString(), name: 'Nguyễn Văn A' }]);
    setEditingContact(null);
  };

  // Sửa contact
  const handleEditContact = (contact: any) => {
    setEditingContact(contact);
  };

  // Xóa contact
  const handleDeleteContact = (contactId: string) => {
    setContacts(prev => prev.filter(c => c.id !== contactId));
  };

  // Lưu contact (thêm mới hoặc cập nhật)
  const handleSaveContact = () => {
    setEditingContact(null);
  };

  const onDecisionMakerChange = (checked: boolean, record: any) => {
    setContacts(prev => prev.map(c => (c.id === record.id ? { ...c, isDecisionMaker: checked } : c)));
  };

  const onNameChange = (e: any, record: any) => {
    setContacts(prev => prev.map(c => (c.id === record.id ? { ...c, name: e.target.value } : c)));
  };

  //onEmialChange
  const onEmailChange = (e: any, record: any) => {
    setContacts(prev => prev.map(c => (c.id === record.id ? { ...c, email: e.target.value } : c)));
  };

  const onPhoneChange = (e: any, record: any) => {
    setContacts(prev => prev.map(c => (c.id === record.id ? { ...c, phone: e.target.value } : c)));
  };
  const onPositionChange = (val: any, record: any) => {
    setContacts(prev => prev.map(c => (c.id === record.id ? { ...c, position: val } : c)));
  };
  const onNoteChange = (e: any, record: any) => {
    setContacts(prev => prev.map(c => (c.id === record.id ? { ...c, note: e.target.value } : c)));
  };

  const columns: any = [
    {
      title: 'STT',
      dataIndex: 'stt',
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Tên người liên hệ',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      align: 'center',
      render: (value: string, record: any) => {
        return editingContact?.id === record.id ? (
          <>
            <Input
              value={value}
              onChange={e => onNameChange(e, record)}
              maxLength={255}
              status={helper.validateLength(record.name, 1, 255) ? 'error' : ''}
            />
            {helper.validateLength(record.name, 1, 255) && (
              <div style={{ color: 'red', fontSize: '12px' }}>{helper.validateLength(record.name, 1, 255)}</div>
            )}
          </>
        ) : (
          value
        );
      },
    },
    {
      title: 'Chức vụ',
      dataIndex: 'position',
      key: 'position',
      width: 150,
      align: 'center',
      render: (value: string, record: any) => {
        return editingContact?.id === record.id ? (
          <Select
            allowClear
            style={{ width: '100%' }}
            value={value}
            onChange={val => onPositionChange(val, record)}
            options={helper.convertObjToArray(ECustomer.ECustomerPosition).map(item => ({
              value: item.value,
              label: item.name,
            }))}
          />
        ) : (
          ECustomer.ECustomerPosition[value]?.name
        );
      },
    },
    {
      title: 'Người quyết định',
      dataIndex: 'isDecisionMaker',
      key: 'isDecisionMaker',
      width: 120,
      align: 'center',
      render: (value: boolean, record: any) => {
        return editingContact?.id === record.id ? (
          <Checkbox onChange={e => onDecisionMakerChange(e.target.checked, record)} checked={value} />
        ) : value ? (
          '✓'
        ) : (
          ''
        );
      },
    },
    {
      title: 'Số điện thoại',
      dataIndex: 'phone',
      key: 'phone',
      width: 150,
      align: 'center',
      render: (value: string, record: any) => {
        return editingContact?.id === record.id ? (
          <Input
            value={value}
            onChange={e => onPhoneChange(e, record)}
            maxLength={11}
            inputMode="numeric" // Gợi ý bàn phím số trên thiết bị di động
          />
        ) : (
          value
        );
      },
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      width: 200,
      align: 'center',
      render: (value: string, record: any) => {
        return editingContact?.id === record.id ? (
          <Input value={value} onChange={e => onEmailChange(e, record)} type="email" />
        ) : (
          value
        );
      },
    },
    {
      title: 'Ghi chú',
      dataIndex: 'note',
      key: 'note',
      width: 150,
      align: 'center',
      render: (value: string, record: any) => {
        return editingContact?.id === record.id ? (
          <Input.TextArea value={value} onChange={e => onNoteChange(e, record)} />
        ) : (
          value
        );
      },
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 100,
      align: 'center',
      render: (_, record: any) => (
        <div style={{ display: 'flex', gap: 8, justifyContent: 'center' }}>
          {editingContact?.id === record.id ? (
            <Button type="link" icon={<SaveOutlined />} onClick={() => handleSaveContact()} size="small" />
          ) : (
            <Button type="link" icon={<EditOutlined />} onClick={() => handleEditContact(record)} size="small" />
          )}
          <Button
            type="link"
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteContact(record.id!)}
            danger
            size="small"
          />
        </div>
      ),
    },
  ];

  const modalContent = (
    <Form form={form} layout="vertical" onFinish={handleSave}>
      <Card title="Thông tin chung">
        <Row gutter={16}>
          <Col span={6}>
            <Form.Item
              label="Tên khách hàng"
              name="name"
              rules={[{ required: true, message: 'Vui lòng nhập tên khách hàng (Tối đa 255 ký tự)', max: 255 }]}
            >
              <Input placeholder="Nhập Tên khách hàng" maxLength={255} />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              label="Mã số thuế"
              name="taxNumber"
              rules={[{ required: true, message: 'Vui lòng nhập mã số thuế (9-20 ký tự)', min: 9, max: 20 }]}
            >
              <Input placeholder="Nhập Mã số thuế" maxLength={20} />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              label="Số điện thoại"
              name="phone"
              rules={[
                { required: true, message: 'Vui lòng nhập số điện thoại (9-11 số)', min: 9, max: 11 },
                { pattern: /^[0-9]*$/, message: 'Số điện thoại chỉ được chứa số' },
              ]}
            >
              <Input placeholder="Nhập Số điện thoại" maxLength={11} />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              label="Email"
              name="email"
              rules={[
                {
                  required: true,
                  message: 'Vui lòng nhập email',
                },
                {
                  pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                  message: 'Email không hợp lệ',
                },
                { max: 255, message: 'Email không được vượt quá 255 ký tự' },
              ]}
            >
              <Input placeholder="Nhập Email" maxLength={255} />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="Fax" name="fax" rules={[{ max: 30, message: 'Fax không được vượt quá 30 ký tự' }]}>
              <Input placeholder="Nhập Fax" maxLength={30} />
            </Form.Item>
          </Col>

          <Col span={6}>
            <Form.Item label="Nguồn khách hàng" name="source">
              <Select placeholder="-- Vui lòng chọn --">
                {helper.convertObjToArray(ECustomer.ECustomerSource).map(item => (
                  <Select.Option value={item.value}>{item.name}</Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              label="Thị trường"
              name="market"
              rules={[{ required: true, message: 'Vui lòng chọn thị trường' }]}
            >
              <Select placeholder="-- Vui lòng chọn --">
                {helper.convertObjToArray(NSCustomer.MARKET).map(item => (
                  <Select.Option value={item.value}>{item.name}</Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Nhân viên phụ trách"
              name="salesRep"
              rules={[{ required: true, message: 'Vui lòng chọn nhân viên phụ trách' }]}
            >
              <Select mode="multiple" maxCount={1} placeholder="-- Vui lòng chọn --">
                {employees?.map(item => (
                  <Select.Option key={item.id} value={item.id}>
                    {/* {item.code || ''} - {item.fullName} - {item?.positionName || ''} - {item?.departmentName || ''} */}
                    {item.fullName}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider />
      <Card
        title="Thông tin liên hệ"
        extra={
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAddContact}>
            Thêm mới
          </Button>
        }
      >
        <BaseTable columns={columns} data={contacts} total={contacts.length} isLoading={false} />
      </Card>

      <div
        style={{
          textAlign: 'right',
          marginTop: 24,
          borderTop: '1px solid #f0f0f0',
          paddingTop: 16,
        }}
      >
        <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={isCreatingCustomer}>
          Lưu
        </Button>
      </div>

      {/* Contact Modal
      <ModalContact
        editingContact={editingContact}
        contactModalOpen={contactModalOpen}
        setContactModalOpen={setContactModalOpen}
        contactForm={contactForm}
        handleSaveContact={handleSaveContact}
      /> */}
    </Form>
  );

  return (
    <Drawer
      title="Tạo khách hàng"
      open={open}
      onClose={onClose}
      width={1200}
      extra={
        <Button onClick={onClose} style={{ marginRight: 8 }}>
          Hủy
        </Button>
      }
    >
      {modalContent}
    </Drawer>
  );
};

export default CreateCustomerModal;
