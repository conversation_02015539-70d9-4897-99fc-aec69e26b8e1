# 为什么选择我们? {#why}

## 现实问题 {#the-problems}

我多年从事前端开发，主要使用 React 框架开发单页面应用。但是每次启动项目想要找一个合适的框架，确实难上加难，大部分框架都是开发者自己学习 React 搭建的，要么就是不维护了，Vue.js 社区的框架百花齐放，React 社区的框架寥寥无几，如同沙漠。但是开发中后台系统 antd 仍是当仁不让的首选，所以使用 React 开发则是更好的选择。

此时，就需要一个框架集成路由、状态管理、权限管理、代码格式化等基础功能，开箱即用，而不是从头开始搭建，于是基于本人的开发经验并借鉴社区优秀框架，开发了 react-antd-admin。

## 为什么不选择 UmiJS? {#why-not-umijs}

> 声明：本人认为 UmiJS 非常优秀。

UmiJS 是一个非常优秀的框架，但是它太重了，自己封装了很多东西，上手成本很高，对于新手来说不太友好，而且遇到问题很难定位，也很难修改代码，最重要的是包不是最新的无法享受新技术带来的体验。

## 感谢

感谢社区优秀的框架，本项目开发期间主要参考了以下几个项目：

1. [vben-admin](https://github.com/anncwb/vben-admin)
2. [vue-pure-admin](https://github.com/xiaoxian521/vue-pure-admin)

## 其他优秀项目

我在 GitHub 建了两个目录，分别收集了优秀的 React 和 Vue 框架。

### React 框架

点击查看目录：[React Template](https://github.com/stars/condorheroblog/lists/react-template)

### Vue 框架

点击查看目录：[Vue Template](https://github.com/stars/condorheroblog/lists/vue-template)
