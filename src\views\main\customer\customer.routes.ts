import { ContainerLayout } from '~/layout';
import { $t } from '~/locales';
import type { AppRouteRecordRaw } from '~/router/types';
import { CUSTOMER_MENU_ORDER } from '~/views/main/main-menu.order';

import { ExperimentOutlined } from '@ant-design/icons';
import { createElement } from 'react';
import { ACCOUNT_LIST_PATH, CUSTOMER_PATH } from './customer.path';
import ListCustomerView from './list-customer';

const routes: AppRouteRecordRaw[] = [
  {
    path: CUSTOMER_PATH,
    Component: ContainerLayout,
    handle: {
      order: CUSTOMER_MENU_ORDER.CUSTOMER,
      title: $t('common.menu.customer'),
      icon: createElement(ExperimentOutlined),
    },
    children: [
      {
        path: ACCOUNT_LIST_PATH,
        Component: ListCustomerView,
        handle: {
          title: $t('common.menu.listCustomer'),
          icon: createElement(ExperimentOutlined),
        },
      },
    ],
  },
];

export default routes;
