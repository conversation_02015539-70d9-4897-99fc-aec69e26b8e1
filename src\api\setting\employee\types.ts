import { IPageRequest, IPageResponse } from '../../@common/types';
import { ICatalog } from '../catalog/types';

export enum EEmployeeStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  DRAFT = 'DRAFT',
}

export interface ICreateEmployee {
  code: string;
  fullName: string;
  positionName?: string;
  departmentName?: string;
  description?: string | null;
  images?: string | null;
  attachments?: string | null;
  status: EEmployeeStatus;
}

export interface IUpdateEmployee extends Partial<ICreateEmployee> {
  id: string;
}

export interface IListEmployee extends IPageRequest {
  code?: string;
  name?: string;
  status?: EEmployeeStatus;
  createdFrom?: string;
  createdTo?: string;
  createdDate?: string[];
}

export interface IEmployee extends ICreateEmployee {
  id: string;
  createdAt: string;
  updatedAt: string;
}

export interface IListCatalogResponse extends IPageResponse<ICatalog> {}
