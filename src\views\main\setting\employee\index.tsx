import { Avatar, Col, Row, Tag } from 'antd';
import moment from 'moment';
import { IListEmployee } from '~/api/setting/employee/types';
import { NSCatalog } from '~/common/enums/catalog.enum';
import BaseCard from '~/components/base-card';
import BaseFilter from '~/components/base-filter/BaseFilter';
import BaseTable from '~/components/base-table';
import { useEmployee } from './hook/useEmployee';

export const EmployeeView = () => {
  const { data, isLoading, total, setPage, loadData } = useEmployee();

  const handleFilter = (values: IListEmployee) => {
    const filteredValues = Object.entries(values || {}).reduce((acc, [key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        acc[key] = value;
      }
      return acc;
    }, {} as IListEmployee);
    setPage(filteredValues);
  };

  const handleReset = () => {
    setPage({});
    loadData();
  };

  const columns: any[] = [
    {
      title: 'Ảnh đại diện',
      dataIndex: 'avatar',
      key: 'avatar',
      width: 100,
      render: (value: string) => (
        <Avatar
          src={value || 'https://ape-devs-co.s3.ap-southeast-1.amazonaws.com/kQX0BgUMhH'}
          size={40}
          onError={() => true}
        />
      ),
    },
    {
      title: 'Họ và tên',
      dataIndex: 'fullName',
      key: 'fullName',
      width: 150,
    },
    {
      title: 'Số điện thoại',
      dataIndex: 'phone',
      key: 'phone',
      width: 150,
    },
    {
      title: 'Phòng ban',
      dataIndex: 'departmentName',
      key: 'departmentName',
      width: 150,
    },
    {
      title: 'Chức vụ',
      dataIndex: 'positionName',
      key: 'positionName',
      width: 150,
    },

    {
      title: 'Ngày tạo',
      dataIndex: 'createdDate',
      key: 'createdDate',
      width: 150,
      render: (value: string) => moment(value).format('DD/MM/YYYY'),
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 150,
      render: (status: string) => {
        const isActive = status === NSCatalog.EStatus.ACTIVE.code;
        return <Tag color={isActive ? 'green' : 'red'}>{isActive ? 'Đang hoạt động' : 'Ngừng hoạt động'}</Tag>;
      },
    },
  ];

  const filters = [
    {
      key: 'name',
      name: 'Họ và tên',
      type: 'input',
    },
    {
      key: 'department',
      name: 'Phòng ban',
      type: 'input',
    },
    { key: 'position', name: 'Chức vụ', type: 'input' },
    {
      key: 'status',
      name: 'Trạng thái',
      type: 'select',
      selectOptions: [
        { value: 'ACTIVE', name: 'Hoạt động' },
        { value: 'INACTIVE', name: 'Ngừng hoạt động' },
      ],
    },
  ];

  return (
    <BaseCard title="Danh sách nhân viên">
      <Row gutter={16}>
        <Col span={24}>
          <BaseFilter filters={filters} onFilter={handleFilter} onReset={handleReset} isLoading={false} />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable columns={columns} data={data} total={total} isLoading={isLoading} />
        </Col>
      </Row>
    </BaseCard>
  );
};
