{"tenant_account": {"tenant_not_found": "Tenant not found", "username_exists": "Username already exists", "not_found": "Tenant account not found"}, "application": {"not_found": "Application not found", "namespace_not_found": "Application namespace not found", "already_registered": "Application already registered", "not_registered": "Application not registered"}, "auth_client": {"error": {"tenant_not_found": "Tenant not found", "invalid_redirect_uri": "Invalid redirect URI", "invalid_client_credentials_or_redirect_uri": "Invalid client credentials or redirect URI", "account_not_active": "Account is not active", "invalid_password": "Incorrect password", "account_not_register_application": "Account has not registered the application", "account_not_found": "Account not found"}, "account_type_not_allowed": "Account type not allowed", "login": {"error": "Login error"}}}