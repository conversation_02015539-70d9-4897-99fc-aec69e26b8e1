import { NSCustomer } from '~/common/enums/customer.enum';
import { IContact } from '../contact/types';

export interface ICustomerIdReq {
  id: string;
}

export interface ICustomersResponse {
  data: ICustomer[];
  total: number;
}
export interface ListCustomerParams {
  q?: string; // T<PERSON><PERSON> kiếm nhanh theo code/name/email/phone
  code?: string; // code
  name?: string; // tên khách hàng
  email?: string; // email  khách hàng
  phone?: string; // số điện thoại khách hàng
  customerType?: NSCustomer.CustomerType; // loại khách hàng
  source?: NSCustomer.Source; // nguồn khách hàng
  market?: NSCustomer.EMarket; // thị trường
  isActive?: boolean; // trạng thái
  provinceCode?: string; // tỉnh/thành phố
  districtCode?: string; // quận/huyện
  wardCode?: string; // phường/xã
  createdFrom?: string; // từ ngày tạo
  createdTo?: string; // đến ngày tạo
  pageSize?: number; // số lượng trang
  pageIndex?: number; // số trang
}
export interface ICustomer {
  id: string;
  tenantId: any;
  createdDate: string;
  updatedDate: string;
  createdBy: any;
  updatedBy: any;
  code: string;
  name: string;
  email: string;
  phone: string;
  companyName: any;
  taxNumber: string;
  website: any;
  addressLine: any;
  countryCode: any;
  provinceCode: any;
  districtCode: any;
  wardCode: any;
  postalCode: any;
  note: any;
  salesRep?: string[];
  isActive: boolean;
  customerType: string;
  source: string;
  market: string;
  q: string;
  fax: string;
  contacts?: IContact[];
}

export interface Province {
  id: string;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  updatedBy: any;
  isDeleted: boolean;
  code: string;
  name: string;
  regionId: string;
  area: any;
}

export interface District {
  id: string;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  updatedBy: any;
  isDeleted: boolean;
  code: string;
  name: string;
  cityId: string;
}

export interface Ward {
  id: string;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  updatedBy: any;
  isDeleted: boolean;
  code: string;
  name: string;
  districtId: string;
}

export interface ICreateCustomerReq {
  id?: string;
  // Thông tin chung
  name: string; // Tên khách hàng (required)
  taxNumber?: string; // Mã số thuế
  phone?: string; // Số điện thoại
  email?: string; // Email
  fax?: string; // Fax

  // Địa chỉ hành chính
  region?: string; // Khu vực
  provinceCode?: string; // Tỉnh thành
  districtCode?: string; // Quận/Huyện
  wardCode?: string; // Phường/Xã
  address?: string; // Địa chỉ chi tiết

  // Phân loại khách hàng
  customerType?: NSCustomer.CustomerType; // Loại khách hàng
  source?: NSCustomer.Source; // Nguồn khách hàng
  market?: NSCustomer.EMarket; // Thị trường

  // Nhân viên
  salesRep?: string; // Nhân viên phụ trách chính (deprecated - thay bằng employeeIds)
  employeeIds?: string[]; // Danh sách ID nhân viên phụ trách (select multiple)

  // Thông tin liên hệ
  contacts?: ContactInfo[]; // Danh sách người liên hệ

  // File đính kèm
  images?: string[];
}

export interface ContactInfo {
  id?: string;
  customerId?: string;
  name: string; // Tên người liên hệ
  position?: NSCustomer.Position; // Chức vụ
  isDecisionMaker?: boolean; // Người ra quyết định
  email?: string; // Email
  phone?: string; // Số điện thoại
  note?: string; // Ghi chú
}

export interface IUpdateCustomerReq extends Partial<ICreateCustomerReq> {
  id: string;
}

export interface IDeleteCustomerReq {
  id: string;
}

export interface IGetCustomerReq {
  id: string;
}

export interface IGetCustomersReq {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filter?: {
    customerType?: string;
    ranking?: string;
    source?: string;
    industry?: string;
    region?: string;
    salesRep?: string;
    department?: string;
    market?: 'domestic' | 'international';
    isTopInvestor?: boolean;
    isProjectInvestor?: boolean;
    isProjectDesigner?: boolean;
    isProjectContractor?: boolean;
    branch?: string;
    gender?: 'male' | 'female' | 'other';
    nationality?: string;
    addressType?: 'home' | 'office' | 'other';
    city?: string;
    district?: string;
    ward?: string;
    productTrustLevel?: 'high' | 'medium' | 'low';
    interactionLevel?: 'high' | 'medium' | 'low';
    investmentSegment?: 'high' | 'medium' | 'low';
    productAwareness?: 'website' | 'social' | 'friend' | 'other';
    shoppingHabits?: ('promotion' | 'noInterest')[];
  };
}

// detail

export interface ICustomerDetail {
  id: string;
  tenantId: any;
  createdDate: string;
  updatedDate: string;
  createdBy: any;
  updatedBy: any;
  version: number;
  code: string;
  name: string;
  email: string;
  phone: string;
  companyName: any;
  taxNumber: string;
  website: any;
  addressLine: any;
  countryCode: any;
  provinceCode: any;
  districtCode: any;
  wardCode: any;
  address: string;
  region: string;
  postalCode: any;
  note: any;
  salesRep: string;
  isActive: boolean;
  customerType: string;
  source: string;
  market: string;
  q: string;
  fax: string;
  contacts: ICustomerDetailContact[];
}

export interface ICustomerDetailContact {
  id: string;
  tenantId: any;
  createdDate: string;
  updatedDate: string;
  createdBy: any;
  updatedBy: any;
  customerId: string;
  code: string;
  name: string;
  position: string;
  email: string;
  phone: any;
  isDecisionMaker: boolean;
  note: string;
}

export interface ISelectBoxCustomer {
  label: string;
  value: string;
  customerAddress: string;
  customerTaxCode: string;
  contacts: {
    value: string;
    label: string;
    phone: string;
  }[];
}
