import { NSContract } from '~/common/enums/contract.enum';

export interface IContract {
  id?: string;
  code?: string;
  contractNumber?: string;
  name?: string;
  type?: NSContract.EType;
  quoteId?: string;
  templateId?: string;
  signingDate?: Date;
  html?: string;
  status?: NSContract.EStatus;
  createdDate?: Date;
  createBy?: string;
  updatedDate?: Date;
  updateBy?: string;
}
export interface IPaginationContractDto extends IContract {
  pageIndex: number;
  pageSize: number;
}

export interface ICreateContractDto {
  code: string;
  contractNumber: string;
  name: string;
  type: NSContract.EType;
  html: string;
  quoteId: string;
  templateId: string;
}
