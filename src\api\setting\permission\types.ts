import { IPageRequest } from "~/api/@common";

export interface IPermission {
  id: string;
  name: string;
  canView: boolean;
  canUpdate: boolean;
  canCreate: boolean;
}

export interface IPermissionResponse {
  data: IPermission[];
  total: number;
}

export interface IPermissionFilter {
  id: string;
  name: string;
  pageIndex: number;
  pageSize: number;
}

export interface IPermissionMemberFilter extends IPageRequest {
  memberId: string;
}

export interface IPermissionMemberCreate {
  /** ID của nhân viên */
  memberId: string;

  /** Danh sách quyền tài nguyên */
  permissionResourceLst: IPermissionResourceItem[];

  /** Nếu user chưa login, tạ<PERSON> tài kho<PERSON>n CRM */
  username?: string;
  providerId?: string;
  fullName?: string;
  avatar?: string;
  tenantId?: string;
}

export interface IPermissionResourceItem {
  permissionResourceId: string;
  canView: boolean;
  canUpdate: boolean;
  canCreate: boolean;
}
