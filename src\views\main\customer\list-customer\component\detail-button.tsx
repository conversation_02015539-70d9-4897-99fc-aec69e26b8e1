import { EyeOutlined } from '@ant-design/icons';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { ICustomer } from '~/api/customer/types';

import BaseButton from '~/components/base-button';
import BaseModal from '~/components/base-modal';
import { TabsCustomerDetail } from './tabs-customer-detail';

interface DetailButtonProps {
  data: ICustomer;
}

const DetailButton = ({ data }: DetailButtonProps) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [id, setId] = useState('');

  const openModal = () => setOpen(true);
  const closeModal = () => setOpen(false);

  return (
    <>
      <BaseButton
        icon={<EyeOutlined />}
        onClick={() => {
          setId(data.id);
          openModal();
        }}
        type="primary"
      />
      <BaseModal
        open={open}
        title={t('customer.customer.customer_detail.title')}
        description={t('customer.customer.customer_detail.description')}
        onClose={closeModal}
        childrenBody={<TabsCustomerDetail id={id} />}
      />
    </>
  );
};

export default DetailButton;
