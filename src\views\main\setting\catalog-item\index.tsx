import { EditOutlined, PlayCircleOutlined, PlusOutlined, StopOutlined } from '@ant-design/icons';
import { Button, Space, Tag, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { useState } from 'react';
import { NSCatalog } from '~/common/enums/catalog.enum';
import BaseConfirmButton from '~/components/base-confirm-button';
import BaseTable from '~/components/base-table';
import BaseCard from '~/components/base-card';
import BaseFilter from '~/components/base-filter/BaseFilter';
import CreateCatalogModal from './components/create-catalog-modal';
import { useCatalogItem } from './hook/useCatalogItem';
import { ICatalogItem } from '~/api/setting/catalog-item/types';
import UpdateCatalogItemModal from './components/update-catalog-modal';

export interface CatalogItem {
  id?: string;
  itemCategory?: string;
  itemGroup?: string;
  code?: string;
  name?: string;
  unit?: string;
  unitPrice?: number;
  currency?: string;
  description?: string;
  status?: string;
  createdDate?: string;
  createdBy?: string;
}

// Dữ liệu giả cho bộ lọc
const mockFilterFields = [
  {
    key: 'code',
    name: 'Mã sản phẩm',
    type: 'input',
  },
  {
    key: 'name',
    name: 'Tên sản phẩm',
    type: 'input',
  },
  {
    key: 'status',
    name: 'Trạng thái',
    type: 'select',
    selectOptions: [
      { value: 'ACTIVE', name: 'Đang hoạt động' },
      { value: 'INACTIVE', name: 'Ngưng hoạt động' },
    ],
  },
  {
    key: 'createdDate',
    name: 'Ngày tạo',
    type: 'dateRange',
  },
];

export const CatalogItemView = () => {
  const { data, isLoading, total, active, inactive, page, setPage } = useCatalogItem();
  const [item, setItem] = useState<ICatalogItem | null>(null);
  const [visibleCreateModal, setVisibleCreateModal] = useState(false);
  const [visibleUpdateModal, setVisibleUpdateModal] = useState(false);

  // Hàm xử lý lọc dữ liệu
  const handleFilter = (values: any) => {
    setPage(prev => ({
      ...prev,
      pageIndex: 1,
      ...values,
    }));
  };

  // Hàm reset bộ lọc
  const handleFilterReset = () => {
    setPage(prev => ({
      pageIndex: 1,
      pageSize: 10,
    }));
  };

  // Hàm xử lý kích hoạt/vô hiệu hóa sản phẩm
  const handleSetActiveStatus = (id: string | undefined, newStatus: string) => {
    if (newStatus === NSCatalog.EStatus.ACTIVE.code) {
      active(id);
    } else {
      inactive(id);
    }
  };

  const columns: ColumnsType<ICatalogItem> = [
    {
      title: 'STT',
      dataIndex: 'index',
      key: 'index',
      render: (_text, _record, index) => index + 1,
    },
    {
      title: 'Mã sản phẩm',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: 'Tên sản phẩm',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const color = status === NSCatalog.EStatus.ACTIVE.code ? 'green' : 'red';
        const label = status === NSCatalog.EStatus.ACTIVE.code ? 'Đang hoạt động' : 'Ngưng hoạt động';

        return <Tag color={color}>{label}</Tag>;
      },
    },
    {
      title: 'Tác vụ',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Tooltip title="Chỉnh sửa">
            <Button size="small" icon={<EditOutlined />} type="primary" onClick={() => handleEdit(record)} />
          </Tooltip>
          <BaseConfirmButton
            icon={record.status === NSCatalog.EStatus.ACTIVE.code ? <StopOutlined /> : <PlayCircleOutlined />}
            tooltip={record.status === NSCatalog.EStatus.ACTIVE.code ? 'Ngưng hoạt động' : 'Hoạt động lại'}
            confirmTitle={
              record.status === NSCatalog.EStatus.ACTIVE.code
                ? 'Bạn có chắc muốn ngưng hoạt động sản phẩm này?'
                : 'Bạn có chắc muốn hoạt động lại sản phẩm này?'
            }
            type="default"
            danger={record.status === NSCatalog.EStatus.ACTIVE.code}
            onConfirm={() => {
              if (record.status === NSCatalog.EStatus.ACTIVE.code) {
                handleSetActiveStatus(record.id, NSCatalog.EStatus.INACTIVE.code);
              } else {
                handleSetActiveStatus(record.id, NSCatalog.EStatus.ACTIVE.code);
              }
            }}
          />
        </Space>
      ),
    },
  ];

  const onPageChange = (newPageIndex: number, newPageSize: number) => {
    setPage({
      ...page,
      pageIndex: newPageIndex,
      pageSize: newPageSize,
    });
  };

  const handleEdit = (data: ICatalogItem) => {
    setVisibleUpdateModal(true);
    setItem(data);
  };

  return (
    <BaseCard
      title="Thiết lập sản phẩm"
      buttons={[
        {
          text: 'Thêm mới',
          icon: <PlusOutlined />,
          onClick: () => {
            setVisibleCreateModal(true);
          },
          type: 'primary',
        },
      ]}
    >
      {visibleCreateModal && (
        <CreateCatalogModal
          open={visibleCreateModal}
          onClose={() => setVisibleCreateModal(false)}
          onSuccess={() => {
            setVisibleCreateModal(false);
            setPage(prev => ({ ...prev }));
          }}
        />
      )}
      {visibleUpdateModal && (
        <UpdateCatalogItemModal
          open={visibleUpdateModal}
          onClose={() => setVisibleUpdateModal(false)}
          onSuccess={() => {
            setVisibleUpdateModal(false);
            setPage(prev => ({ ...prev }));
          }}
          item={item}
        />
      )}
      {/* Bộ lọc */}
      <div style={{ marginBottom: 16 }}>
        <BaseFilter
          onFilter={handleFilter}
          onReset={handleFilterReset}
          isLoading={isLoading}
          filters={mockFilterFields}
        />
      </div>
      <BaseTable
        columns={columns}
        data={data || []}
        total={total || 0}
        isLoading={isLoading}
        onPageChange={onPageChange}
      />
    </BaseCard>
  );
};

export default CatalogItemView;
