import { Col, Form, Input, Row } from 'antd';
import BaseDrawer from '~/components/base-drawer';
import RichTextEditor from '~/components/text-editor';

export default function UploadTemplateContractDrawer({
  open,
  onClose,
  onSubmit,
}: {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: any) => Promise<void>;
}) {
  const [form] = Form.useForm();
  const handleSubmit = async (values: any) => {
    await onSubmit(values);
  };
  return (
    <BaseDrawer
      title="Tải lên mẫu hợp đồng"
      buttons={[
        {
          text: 'Lưu',
          type: 'primary',
          onClick: () => form.submit(),
        },
      ]}
      open={open}
      onClose={onClose}
      width="50%"
    >
      <Form layout="vertical" form={form} onFinish={handleSubmit}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="Tên mẫu" name="name" rules={[{ required: true, message: '<PERSON><PERSON> lòng nhập tên mẫu' }]}>
              <Input placeholder="Nhập tên mẫu" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Mã hợp đồng"
              name="code"
              rules={[{ required: true, message: 'Vui lòng nhập mã hợp đồng' }]}
            >
              <Input placeholder="Nhập mã hợp đồng" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item label="Nội dung mẫu" name="html">
          <RichTextEditor value={form.getFieldValue('html')} onChange={value => form.setFieldValue('html', value)} />
        </Form.Item>
      </Form>
    </BaseDrawer>
  );
}
