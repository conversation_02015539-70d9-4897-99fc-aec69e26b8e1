import {
  BarChartOutlined,
  EnvironmentOutlined,
  FileOutlined,
  HistoryOutlined,
  InfoCircleOutlined,
  PhoneOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { Space, Tabs } from 'antd';
import { useTranslation } from 'react-i18next';

import {
  AddressTab,
  ComplaintTab,
  ContactTab,
  DocumentTab,
  HistoryTab,
  InformationTab,
  RevenueTab,
} from './tab-detail';
import useCustomerContact from '../../Hooks/useCustomerContact';
import { useEffect, useState } from 'react';
import useCustomer from '../../Hooks/useCustomer';

export const TabsCustomerDetail = ({ id }: { id: string }) => {
  const { t } = useTranslation();
  const { detailCustomer } = useCustomer();
  // Dữ liệu mẫu cho khách hàng
  let [data, setData]: any = useState<any>();

  useEffect(() => {
    detailCustomer(id).then(res => {
      if (res) {
        setData(res);
      }
    });
  }, [id]);

  return (
    <div>
      <Tabs
        defaultActiveKey="1"
        items={[
          {
            key: '1',
            label: (
              <Space>
                <InfoCircleOutlined />
                <span>{t('customer.customer.customer_detail.information_tab.title') || 'Thông tin khách hàng'}</span>
              </Space>
            ),
            children: <InformationTab data={data} />,
          },
          {
            key: '2',
            label: (
              <Space>
                <PhoneOutlined />
                <span>{t('customer.customer.customer_detail.contact_tab.title') || 'Thông tin liên hệ'}</span>
              </Space>
            ),
            children: <ContactTab data={data?.contacts} />,
          },
          {
            key: '3',
            label: (
              <Space>
                <WarningOutlined />
                <span>{t('customer.customer.customer_detail.complaint_tab.title') || 'Thông tin khiếu nại'}</span>
              </Space>
            ),
            children: <ComplaintTab />,
          },
          {
            key: '4',
            label: (
              <Space>
                <EnvironmentOutlined />
                <span>{t('customer.customer.customer_detail.address_tab.title') || 'Thông tin địa chỉ'}</span>
              </Space>
            ),
            children: <AddressTab customerId={data?.id} />,
          },
          {
            key: '6',
            label: (
              <Space>
                <FileOutlined />
                <span>{t('customer.customer.customer_detail.document_tab.title') || 'Tài liệu khách hàng'}</span>
              </Space>
            ),
            children: <DocumentTab />,
          },
          {
            key: '7',
            label: (
              <Space>
                <BarChartOutlined />
                <span>{t('customer.customer.customer_detail.revenue_tab.title') || 'Doanh số khách hàng'}</span>
              </Space>
            ),
            children: <RevenueTab />,
          },
          {
            key: '8',
            label: (
              <Space>
                <HistoryOutlined />
                <span>{t('customer.customer.customer_detail.history_tab.title') || 'Lịch sử thao tác'}</span>
              </Space>
            ),
            children: <HistoryTab />,
          },
        ]}
      />
    </div>
  );
};
