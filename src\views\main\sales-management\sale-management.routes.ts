import { ContainerLayout } from '~/layout';
import type { AppRouteRecordRaw } from '~/router/types';

import { HomeOutlined } from '@ant-design/icons';
import { createElement } from 'react';
import { $t } from '~/locales';
import { SALE_MANAGEMENT_MENU_ORDER } from '~/views/main/main-menu.order';
import { ContractsComponent } from './contract';
import { QuotationsComponent } from './quotation';
import {
  SALE_MANAGEMENT_CONTRACT_PATH,
  SALE_MANAGEMENT_PATH,
  SALE_MANAGEMENT_QUOTATION_PATH,
} from './sale-management.path';
import { QuotationDetailComponent } from './quotation/components/quotation-detail-component';

const routes: AppRouteRecordRaw[] = [
  {
    path: SALE_MANAGEMENT_PATH,
    Component: ContainerLayout,
    handle: {
      order: SALE_MANAGEMENT_MENU_ORDER.SALE_MANAGEMENT,
      title: $t('common.menu.sale_management'),
      icon: createElement(HomeOutlined),
    },
    children: [
      {
        path: SALE_MANAGEMENT_QUOTATION_PATH,
        Component: QuotationsComponent,
        handle: {
          title: $t('common.menu.quotation'),
          icon: createElement(HomeOutlined),
        },
      },
      {
        path: `${SALE_MANAGEMENT_QUOTATION_PATH}/detail`,
        Component: QuotationDetailComponent,

        handle: {
          title: $t('common.menu.quotation_detail'),
          icon: createElement(HomeOutlined),
          hideInMenu: true,
        },
      },

      {
        path: SALE_MANAGEMENT_CONTRACT_PATH,
        Component: ContractsComponent,
        handle: {
          title: $t('common.menu.contract'),
          icon: createElement(HomeOutlined),
        },
      },
    ],
  },
];

export default routes;
