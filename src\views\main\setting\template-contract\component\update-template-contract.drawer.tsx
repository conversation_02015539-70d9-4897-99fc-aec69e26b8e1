import { Col, Form, Input, Row } from 'antd';
import { useEffect, useState } from 'react';
import { IContractTemplate } from '~/api/setting/contract-template/types';
import BaseDrawer from '~/components/base-drawer';
import RichTextEditor from '~/components/text-editor';

export default function UpdateTemplateContractDrawer({
  open,
  onClose,
  data,
  onSubmit,
}: {
  open: boolean;
  onClose: () => void;
  data: IContractTemplate | null;
  onSubmit?: (values: IContractTemplate) => Promise<void>;
}) {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (data) {
      form.setFieldsValue({
        id: data.id,
        name: data.name,
        code: data.code,
        html: data.html,
      });
    }
  }, [data, form]);

  const handleSubmit = async (values: IContractTemplate) => {
    await onSubmit?.(values);
  };

  return (
    <BaseDrawer
      title="Cập nhật mẫu hợp đồng"
      buttons={[
        {
          text: 'Hủy',
          onClick: onClose,
        },
        {
          text: 'Lưu',
          type: 'primary',
          onClick: async () => {
            setLoading(true);
            await handleSubmit(form.getFieldsValue());
            setLoading(false);
          },
        },
      ]}
      open={open}
      onClose={onClose}
      width="50%"
    >
      <Form layout="vertical" form={form}>
        <Form.Item label="ID" name="id" hidden>
          <Input />
        </Form.Item>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="Tên mẫu" name="name" rules={[{ required: true, message: 'Vui lòng nhập tên mẫu' }]}>
              <Input placeholder="Nhập tên mẫu" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Mã hợp đồng"
              name="code"
              rules={[{ required: true, message: 'Vui lòng nhập mã hợp đồng' }]}
            >
              <Input placeholder="Nhập mã hợp đồng" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="Ngày tạo">
              <Input value={new Date(data?.createdDate || '').toLocaleString('vi-VN')} readOnly disabled />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Cập nhật lần cuối">
              <Input value={new Date(data?.updatedDate || '').toLocaleString('vi-VN')} readOnly disabled />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item label="Nội dung mẫu" name="html">
          <RichTextEditor value={form.getFieldValue('html')} onChange={value => form.setFieldValue('html', value)} />
        </Form.Item>
      </Form>
    </BaseDrawer>
  );
}
