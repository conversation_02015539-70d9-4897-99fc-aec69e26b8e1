import React, { useState } from 'react';
import { Input } from 'antd';
import helper from '~/common/helpers/helper';

interface BaseValidateInputProps {
  value: string;
  onChange: (value: string) => void;
  validate: (value: string) => string | undefined;
  placeholder?: string;
  type?: string;
}

const BaseValidateInput: React.FC<BaseValidateInputProps> = ({
  value,
  onChange,
  validate,
  placeholder,
  type,
}) => {
  const [error, setError] = useState<string | undefined>(undefined);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
    setError(validate(newValue));
  };

  return (
    <>
      <Input
        value={value}
        onChange={handleChange}
        placeholder={placeholder}
        type={type}
        status={error ? 'error' : ''}
      />
      {error && <div style={{ color: 'red', fontSize: '12px' }}>{error}</div>}
    </>
  );
};

export default BaseValidateInput;
