import { useCallback, useEffect, useState } from 'react';
import { ICatalog, ICreateCatalog, IUpdateCatalog, IListCatalog } from '~/api/setting/catalog/types';
import { catalogApi } from '~/api/setting/catalog';
import { formatDateCustom } from '~/common/helpers/helper';

export const useCatalog = () => {
  const [data, setData] = useState<ICatalog[]>([]);
  const [detailData, setDetailData] = useState<ICatalog | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState<IListCatalog>({
    pageIndex: 1,
    pageSize: 10,
  });

  const loadData = useCallback(async () => {
    setIsLoading(true);
    const { createdDate, ...rest } = page;
    const newPage = {
      ...rest,
      createdFrom: createdDate ? formatDateCustom(createdDate[0], 'YYYY-MM-DD') : undefined,
      createdTo: createdDate ? formatDateCustom(createdDate[1], 'YYYY-MM-DD') : undefined,
    };
    const result = await catalogApi.list(newPage)
    setData(Array.isArray(result.data) ? [...result.data] : []);
    setTotal(result.total);
    setIsLoading(false);
  }, [page]);

  // detail
  const detail = useCallback(async (id: string) => {
    setIsLoading(true);
    catalogApi
      .detail({ id })
      .then(result => {
        setDetailData(result);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  const create = useCallback(async (data: ICreateCatalog) => {
    setIsLoading(true);
    await catalogApi.create(data)
    await loadData();
    setIsLoading(false);
  }, [loadData]);

  // Update
  const update = useCallback(async (data: IUpdateCatalog) => {
    setIsLoading(true);
    await catalogApi.update(data)
    await loadData();
    setIsLoading(false);
  }, [loadData]);

  // Active
  const active = useCallback((id: string) => {
    setIsLoading(true);
    catalogApi
      .active({ id })
      .then(result => {
        loadData();
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  // Inactive
  const inactive = useCallback((id: string) => {
    setIsLoading(true);
    catalogApi
      .inactive({ id })
      .then(result => {
        loadData();
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    data,
    detailData,
    setData,
    detail,
    isLoading,
    setIsLoading,
    total,
    setTotal,
    page,
    setPage,
    create,
    update,
    active,
    inactive,
  };
};
