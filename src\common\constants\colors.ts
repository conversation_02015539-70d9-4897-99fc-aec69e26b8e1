// <PERSON><PERSON><PERSON> ch<PERSON>h của hệ thống
export const COLORS = {
  // <PERSON><PERSON><PERSON> chính
  PRIMARY: "#fcad16",
  PRIMARY_2: "#2c2d2c",
  PRIMARY_RGB: "rgb(65, 148, 255)",

  // <PERSON><PERSON><PERSON> <PERSON><PERSON> bản
  WHITE: "#ffffff",
  BLACK: "#000000",

  // <PERSON><PERSON>u xám
  GRAY: {
    LIGHT: "#bbbbbb",
    MEDIUM: "#888888",
    DARK: "#444444",
    BORDER: "#6f6c6c",
    SPLIT: "#424242",
    BACKGROUND: "#303030",
    SHADOW: "#dddddd",
    TABLE_BORDER: "#eee",
  },

  // <PERSON><PERSON>u nền
  BACKGROUND: {
    LIGHT: "#fff",
    DARK: "#000",
    DARK_2: "#1f1f1f",
    COMPONENT: "#303030",
    BODY: "#303030",
    POPOVER: "#303030",
  },

  // Màu trạng thái
  STATUS: {
    SUCCESS: "#008a00",
    ERROR: "#e60000",
    WARNING: "#ff9900",
    INFO: "#0066cc",
  },

  // Màu cho editor (ReactQuill)
  EDITOR_COLORS: [
    "#000000",
    "#e60000",
    "#ff9900",
    "#ffff00",
    "#008a00",
    "#0066cc",
    "#9933ff",
    "#ffffff",
    "#facccc",
    "#ffebcc",
    "#ffffcc",
    "#cce8cc",
    "#cce0f5",
    "#ebd6ff",
    "#bbbbbb",
    "#f06666",
    "#ffc266",
    "#ffff66",
    "#66b966",
    "#66a3e0",
    "#c285ff",
    "#888888",
    "#a10000",
    "#b26b00",
    "#b2b200",
    "#006100",
    "#0047b2",
    "#6b24b2",
    "#444444",
    "#5c0000",
    "#663d00",
    "#666600",
    "#003700",
    "#002966",
    "#3d1466",
  ],

  // Màu CSS Variables
  CSS_VARS: {
    PRIMARY: "var(--primary-color)",
    PRIMARY_BG: "var(--primary-bg-color)",
  },
} as const;

// Thêm type cho TypeScript
export type ColorKey = keyof typeof COLORS;
export type GrayColorKey = keyof typeof COLORS.GRAY;
export type BackgroundColorKey = keyof typeof COLORS.BACKGROUND;
export type StatusColorKey = keyof typeof COLORS.STATUS;
