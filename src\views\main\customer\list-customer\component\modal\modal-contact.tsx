import { Button, Checkbox, Col, Form, FormInstance, Input, Modal, Row, Select } from 'antd';
import { IContact } from '~/api/contact/types';
// Thay đổi import ContactInfo từ file CustomerContactInfoTab thay vì từ API

interface ModalContactProps {
  editingContact: IContact | null;
  contactModalOpen: boolean;
  setContactModalOpen: (open: boolean) => void;
  contactForm: FormInstance<IContact>;
  handleSaveContact: (values: IContact) => void;
}

export const ModalContact = ({
  editingContact,
  contactModalOpen,
  setContactModalOpen,
  contactForm,
  handleSaveContact,
}: ModalContactProps) => {
  return (
    <Modal
      title={editingContact ? 'Sửa thông tin liên hệ' : 'Thêm thông tin liên hệ'}
      open={contactModalOpen}
      onCancel={() => {
        setContactModalOpen(false);
        contactForm.resetFields();
      }}
      footer={[
        <Button key="cancel" onClick={() => setContactModalOpen(false)}>
          Hủy
        </Button>,
        <Button key="save" type="primary" onClick={() => contactForm.submit()}>
          {editingContact ? 'Cập nhật' : 'Thêm mới'}
        </Button>,
      ]}
      width={600}
    >
      <Form form={contactForm} layout="vertical" onFinish={handleSaveContact}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Tên người liên hệ"
              name="name"
              rules={[{ required: true, message: 'Vui lòng nhập tên người liên hệ' }]}
            >
              <Input placeholder="Nhập tên người liên hệ" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Chức vụ" name="position" rules={[{ required: true, message: 'Vui lòng nhập chức vụ' }]}>
              <Select
                placeholder="Chọn chức vụ"
                options={[
                  { label: 'Giám đốc', value: 'Giám đốc' },
                  { label: 'Trưởng phòng', value: 'Trưởng phòng' },
                  { label: 'Nhân viên', value: 'Nhân viên' },
                  { label: 'Kế toán', value: 'Kế toán' },
                  { label: 'Khác', value: 'Khác' },
                ]}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Email"
              name="email"
              rules={[
                {
                  required: true,
                  message: 'Vui lòng nhập email',
                  pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                },
              ]}
            >
              <Input placeholder="Nhập email" type="email" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Số điện thoại"
              name="phone"
              rules={[{ required: true, message: 'Vui lòng nhập số điện thoại', pattern: /^[0-9]*$/ }]}
            >
              <Input placeholder="Nhập số điện thoại" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item name="isDecisionMaker" valuePropName="checked">
              <Checkbox defaultChecked={false}>Người ra quyết định</Checkbox>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item label="Ghi chú" name="note">
              <Input.TextArea placeholder="Nhập ghi chú" rows={3} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};
