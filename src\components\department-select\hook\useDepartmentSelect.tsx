import { useCallback, useEffect, useState } from 'react';
import { IDepartment, IListDepartment } from '~/api/setting/department/types';
import { departmentApi } from '~/api/setting/department';

export const useDepartmentSelect = () => {
  const [data, setData] = useState<IDepartment[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [page, setPage] = useState<IListDepartment>({
    pageIndex: 1,
    pageSize: 50,
  });

  const loadData = useCallback(() => {
    setIsLoading(true);
    departmentApi
      .list(page)
      .then(result => {
        setData(result.data);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [page]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    data,
    setData,
    isLoading,
    setIsLoading,
    page,
    setPage,
  };
};
