# Hooks - <PERSON><PERSON><PERSON> Hook Toàn <PERSON>hư mục này chứ<PERSON> các custom hooks đư<PERSON>c sử dụng toàn cục trong ứng dụng. <PERSON><PERSON><PERSON> hooks này cung cấp các chức năng chung và có thể được tái sử dụng ở nhiều component khác nhau.

## Danh sách các hooks có sẵn:

### `useAccess`

Hook quản lý quyền truy cập và phân quyền người dùng trong ứng dụng.

### `useCurrentRoute`

Hook để lấy thông tin về route hiện tại và điều hướng.

### `useDeviceType`

Hook phát hiện loại thiết bị (mobile, tablet, desktop) để responsive design.

### `useLanguage`

Hook quản lý ngôn ngữ và đa ngôn ngữ (i18n) của ứng dụng.

### `useLayoutMenu`

Hook quản lý trạng thái và hành vi của menu layout.

### `usePreferences`

Hook quản lý các tùy chọn và cài đặt người dùng.

### `useScrollToHash`

Hook xử lý việc cuộn đến phần tử có hash tương ứng trong URL.

## Cách sử dụng:

```typescript
import { usePreferences, useDeviceType, useLanguage } from '@/hooks';

function MyComponent() {
  const { preferences, updatePreferences } = usePreferences();
  const deviceType = useDeviceType();
  const { language, changeLanguage } = useLanguage();

  // Sử dụng các hooks...
}
```

## Lưu ý:

- Tất cả các hooks này đều được export từ file `index.ts` để dễ dàng import
- Các hooks được thiết kế để sử dụng toàn cục và có thể được gọi từ bất kỳ component nào
- Mỗi hook có thể có dependencies riêng và logic xử lý phức tạp bên trong
