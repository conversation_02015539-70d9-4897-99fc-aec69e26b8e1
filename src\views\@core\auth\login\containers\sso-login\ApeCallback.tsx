import { useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { useNavigate } from 'react-router';
import { hideLoading } from '~/plugins';
import { useAuthStore } from '~/store/auth.store';

const ApeCallback = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const setTokenState = useAuthStore.setState;
  const params: any = new URLSearchParams(window.location.search);
  const allParams: any = Object.fromEntries(params.entries());
  const accessToken = allParams.accessToken;

  useEffect(() => {
    if (accessToken) {
      setTokenState({
        accessToken,
        refreshToken: allParams.refreshToken || '',
      });

      window.location.href = '/';
      hideLoading();
    } else {
      navigate('/login');
    }
  }, []);

  return <p className="text-black"><PERSON><PERSON> xử lý đăng nhập...</p>;
};

export default ApeCallback;
