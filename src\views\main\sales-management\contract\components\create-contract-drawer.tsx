import { Form, Input, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import { ICreateContractDto } from '~/api/contract/types';
import { IQuotation } from '~/api/quotation/types';
import { IContractTemplate } from '~/api/setting/contract-template/types';
import { CommonEnum } from '~/common/enums';
import { NSContract } from '~/common/enums/contract.enum';
import BaseDrawer from '~/components/base-drawer';
import RichTextEditor from '~/components/text-editor';
import useTemplateContract from '~/views/main/setting/template-contract/hook/use-template-contract';

const LazyRichTextEditor = React.lazy(() => import('~/components/text-editor'));

export default function CreateContractDrawer({
  open,
  onClose,
  onSubmit,
  options,
  length,
}: {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: any) => Promise<void>;
  options?: {
    quote: {
      options: IQuotation[];
    };
    template: {
      options: IContractTemplate[];
    };
  };
  length?: number;
}) {
  const [form] = Form.useForm();
  const [selectTemplate, setSelectTemplate] = useState<string | undefined>('');
  const { detailTemplate, detailData } = useTemplateContract();
  const [submitData, setSubmitData] = useState<ICreateContractDto | null>(null);
  const [isChange, setIsChange] = useState(false);
  useEffect(() => {
    const getContractDetail = async () => {
      if (selectTemplate) {
        await detailTemplate(selectTemplate);
      }
    };
    getContractDetail();
  }, [selectTemplate]);
  const firstSubmit = async (values: ICreateContractDto) => {
    setSelectTemplate(values?.templateId);
    setSubmitData({
      ...values,
    });
  };
  const handleSubmit = async () => {
    if (isChange && submitData) {
      const req = {
        ...submitData,
        html: form.getFieldValue('html'),
      };
      await onSubmit(req);
      setIsChange(false);
    } else {
      const req = {
        ...submitData,
        html: detailData?.html,
      };
      await onSubmit(req);
    }
  };
  const contractTypeOptions = Object.values(NSContract.EType).map(item => {
    const map: Record<string, string> = {
      SALES: 'Hợp đồng bán hàng',
      SERVICE: 'Hợp đồng dịch vụ',
      MAINTENANCE: 'Hợp đồng bảo trì',
      CONSULTATION: 'Hợp đồng tư vấn',
      DISTRIBUTION: 'Hợp đồng phân phối',
      PURCHASE: 'Hợp đồng mua hàng',
    };
    return {
      label: map[item] || item,
      value: item,
    };
  });
  const filterTemplateOptions = options?.template?.options?.filter(
    item => item.status === CommonEnum.CommonStatus.ACTIVE,
  );
  const contractNumber = `HD00${length + 1}`;
  return (
    <BaseDrawer
      title="Tạo mới hợp đồng"
      buttons={[
        {
          text: 'Tạo mới',
          type: 'primary',
          onClick: () => form.submit(),
        },
      ]}
      open={open}
      onClose={onClose}
      width="50%"
    >
      <Form layout="vertical" form={form} onFinish={firstSubmit}>
        <Form.Item label="Số hợp đồng" name="contractNumber" initialValue={contractNumber}>
          <Input disabled />
        </Form.Item>
        <Form.Item label="Mã hợp đồng" name="code" rules={[{ required: true, message: 'Vui lòng nhập mã hợp đồng' }]}>
          <Input placeholder="Nhập mã hợp đồng" />
        </Form.Item>

        <Form.Item label="Tên hợp đồng" name="name" rules={[{ required: true, message: 'Vui lòng nhập tên hợp đồng' }]}>
          <Input placeholder="Nhập tên hợp đồng" />
        </Form.Item>

        <Form.Item
          label="Loại hợp đồng"
          name="type"
          rules={[{ required: true, message: 'Vui lòng chọn loại hợp đồng' }]}
        >
          <Select placeholder="Chọn loại hợp đồng" options={contractTypeOptions} />
        </Form.Item>

        <Form.Item label="Chọn báo giá" name="quoteId" rules={[{ required: true, message: 'Vui lòng chọn báo giá' }]}>
          <Select
            placeholder="Chọn báo giá"
            options={options?.quote?.options?.map(item => ({
              label: item.quotationNumber,
              value: item.id,
            }))}
          />
        </Form.Item>
        <Form.Item
          label="Chọn mẫu hợp đồng"
          name="templateId"
          rules={[{ required: true, message: 'Vui lòng chọn mẫu hợp đồng' }]}
        >
          <Select
            placeholder="Chọn mẫu hợp đồng"
            options={filterTemplateOptions?.map(item => ({
              label: item.name,
              value: item.id,
            }))}
          />
        </Form.Item>

        <BaseDrawer
          title="Tạo mới hợp đồng"
          open={Boolean(selectTemplate)}
          onClose={() => setSelectTemplate('')}
          width="50%"
          buttons={[
            {
              text: 'Tạo mới hợp đồng',
              type: 'primary',
              onClick: () => handleSubmit(),
            },
          ]}
        >
          <RichTextEditor
            value={detailData?.html || ''}
            onChange={value => {
              setIsChange(true);
              form.setFieldValue('html', value);
            }}
            readOnly={false}
          />
        </BaseDrawer>
      </Form>
    </BaseDrawer>
  );
}
