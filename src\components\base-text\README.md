# BaseText Component

Component `BaseText` là một component Text có thể tái sử dụng được xây dựng trên `antd Typography.Text`, cung cấp các props thuận tiện để styling text.

## Cách sử dụng

```tsx
import { BaseText } from '@/components';
// hoặc
import BaseText from '@/components/BaseText';

// Sử dụng cơ bản
<BaseText>Text content</BaseText>

// Với variant
<BaseText variant="h1">Heading 1</BaseText>
<BaseText variant="body2">Body text</BaseText>

// Với màu sắc
<BaseText color="primary">Primary text</BaseText>
<BaseText color="error">Error text</BaseText>

// Với font weight
<BaseText weight="bold">Bold text</BaseText>

// Với size
<BaseText size="lg">Large text</BaseText>

// Kết hợ<PERSON> nhi<PERSON> props
<BaseText
  variant="h2"
  color="primary"
  weight="bold"
  align="center"
>
  Styled heading
</BaseText>
```

## Props

| Prop         | Type                                                                                                            | Default         | Description                          |
| ------------ | --------------------------------------------------------------------------------------------------------------- | --------------- | ------------------------------------ |
| `children`   | `React.ReactNode`                                                                                               | -               | Nội dung text                        |
| `variant`    | `'body1' \| 'body2' \| 'caption' \| 'subtitle1' \| 'subtitle2' \| 'h1' \| 'h2' \| 'h3' \| 'h4' \| 'h5' \| 'h6'` | `'body1'`       | Kiểu text (tự động set size phù hợp) |
| `color`      | `'primary' \| 'secondary' \| 'success' \| 'warning' \| 'error' \| 'info' \| 'textPrimary' \| 'textSecondary'`   | `'textPrimary'` | Màu text                             |
| `weight`     | `'light' \| 'normal' \| 'medium' \| 'semibold' \| 'bold'`                                                       | `'normal'`      | Độ đậm font                          |
| `size`       | `'xs' \| 'sm' \| 'md' \| 'lg' \| 'xl' \| '2xl' \| '3xl'`                                                        | -               | Size custom (override variant size)  |
| `align`      | `'left' \| 'center' \| 'right' \| 'justify'`                                                                    | `'left'`        | Căn chỉnh text                       |
| `transform`  | `'none' \| 'capitalize' \| 'uppercase' \| 'lowercase'`                                                          | `'none'`        | Transform text                       |
| `decoration` | `'none' \| 'underline' \| 'line-through'`                                                                       | `'none'`        | Text decoration                      |
| `truncate`   | `boolean`                                                                                                       | `false`         | Cắt bớt text khi quá dài             |
| `maxLines`   | `number`                                                                                                        | -               | Số dòng tối đa khi truncate          |
| `style`      | `React.CSSProperties`                                                                                           | -               | Custom styles                        |
| `className`  | `string`                                                                                                        | -               | CSS class                            |
| `onClick`    | `() => void`                                                                                                    | -               | Click handler                        |
| `id`         | `string`                                                                                                        | -               | Element ID                           |

## Ví dụ nâng cao

```tsx
// Text với truncate
<BaseText truncate maxLines={2} style={{ width: '200px' }}>
  Đây là một đoạn text rất dài sẽ được cắt bớt sau 2 dòng
</BaseText>

// Text có thể click
<BaseText
  color="primary"
  decoration="underline"
  onClick={() => console.log('Clicked!')}
>
  Clickable text
</BaseText>

// Text với style custom
<BaseText
  variant="h3"
  style={{
    background: 'linear-gradient(45deg, #f00, #00f)',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent'
  }}
>
  Gradient text
</BaseText>
```
