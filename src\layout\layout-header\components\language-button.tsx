import type { ButtonProps, MenuProps } from 'antd';
import type { LanguageType } from '~/locales';

import { BasicButton } from '~/components';
import { useLanguage } from '~/hooks';
import { getLanguageItems } from '~/layout/widgets/preferences/blocks/general/utils';

import { TranslationOutlined } from '@ant-design/icons';
import { Dropdown } from 'antd';

export function LanguageButton({ ...restProps }: ButtonProps) {
  const { language, setLanguage } = useLanguage();

  const items: MenuProps['items'] = getLanguageItems();

  const onClick: MenuProps['onClick'] = ({ key }) => {
    setLanguage(key as LanguageType);
  };

  return (
    <Dropdown
      menu={{
        items,
        onClick,
        selectable: true,
        selectedKeys: [language],
      }}
      trigger={['click']}
      arrow={false}
    >
      <BasicButton type="text" {...restProps}>
        <TranslationOutlined />
      </BasicButton>
    </Dropdown>
  );
}
