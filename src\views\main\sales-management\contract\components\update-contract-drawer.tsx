import { Col, Form, Input, Row, Select } from 'antd';
import { useEffect, useState } from 'react';
import 'react-quill/dist/quill.snow.css';
import { IContract } from '~/api/contract/types';
import { IQuotation } from '~/api/quotation/types';
import { IContractTemplate } from '~/api/setting/contract-template/types';
import { NSContract } from '~/common/enums/contract.enum';
import BaseDrawer from '~/components/base-drawer';
import RichTextEditor from '~/components/text-editor';
import useTemplateContract from '~/views/main/setting/template-contract/hook/use-template-contract';

export default function UpdateContractDrawer({
  open,
  onClose,
  data,
  onSubmit,
  options,
}: {
  open: boolean;
  onClose: () => void;
  data: IContract | null;
  options?: {
    quote: {
      options: IQuotation[];
    };
    template: {
      options: IContractTemplate[];
    };
  };
  onSubmit?: (values: IContract) => void;
}) {
  const [form] = Form.useForm();
  const [childrenContractDrawer, setChildrenContractDrawer] = useState(false);
  const [childrenQuoteDrawer, setChildrenQuoteDrawer] = useState(false);
  const [editedHtml, setEditedHtml] = useState<string | undefined>(undefined);

  const [selectedTemplateId, setSelectedTemplateId] = useState<string | undefined>();
  const { detailTemplate, detailData } = useTemplateContract();

  const handleTemplateChange = async (templateId: string) => {
    setSelectedTemplateId(templateId);
    try {
      await detailTemplate(templateId); // gọi API lấy HTML mẫu
    } catch (error) {
      console.error('Error fetching template:', error);
    }
  };

  useEffect(() => {
    if (detailData?.html) {
      setEditedHtml(detailData.html);
      form.setFieldValue('html', detailData.html);
    }
  }, [detailData, form]);

  const onChildrenContractDrawerClose = () => {
    if (editedHtml !== undefined) {
      form.setFieldValue('html', editedHtml);
    }
    setChildrenContractDrawer(false);
  };

  const onChildrenQuoteDrawerClose = () => {
    setChildrenQuoteDrawer(false);
  };

  // Khi load data từ prop
  useEffect(() => {
    if (data) {
      form.setFieldsValue({
        id: data.id,
        contractNumber: data.contractNumber,
        code: data.code,
        name: data.name,
        type: data.type,
        html: data.html,
        quoteId: data.quoteId,
      });

      setEditedHtml(data.html);
      setSelectedTemplateId(data.templateId);
    }
  }, [data, form]);

  const contractTypeOptions = Object.values(NSContract.EType).map(item => {
    const map: Record<string, string> = {
      SALES: 'Hợp đồng bán hàng',
      SERVICE: 'Hợp đồng dịch vụ',
      MAINTENANCE: 'Hợp đồng bảo trì',
      CONSULTATION: 'Hợp đồng tư vấn',
      DISTRIBUTION: 'Hợp đồng phân phối',
      PURCHASE: 'Hợp đồng mua hàng',
    };
    return {
      label: map[item] || item,
      value: item,
    };
  });

  const handleSubmit = async () => {
    const values = form.getFieldsValue();
    const finalValues = {
      ...values,
      templateId: selectedTemplateId,
      html: form.getFieldValue('html'),
    };
    onSubmit?.(finalValues);
  };

  return (
    <BaseDrawer
      title="Cập nhật hợp đồng"
      buttons={[
        {
          text: 'Xem hợp đồng',
          type: 'default',
          onClick: () => setChildrenContractDrawer(true),
        },
        {
          text: 'Lưu',
          type: 'primary',
          onClick: handleSubmit,
        },
      ]}
      open={open}
      onClose={onClose}
      width="50%"
    >
      <Form layout="vertical" form={form}>
        <Form.Item name="id" hidden>
          <Input disabled />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Số hợp đồng"
              name="contractNumber"
              rules={[{ required: true, message: 'Vui lòng nhập số hợp đồng' }]}
            >
              <Input disabled />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Mã hợp đồng"
              name="code"
              rules={[{ required: true, message: 'Vui lòng nhập mã hợp đồng' }]}
            >
              <Input />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Tên hợp đồng"
              name="name"
              rules={[{ required: true, message: 'Vui lòng nhập tên hợp đồng' }]}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Loại hợp đồng"
              name="type"
              rules={[{ required: true, message: 'Vui lòng chọn loại hợp đồng' }]}
            >
              <Select options={contractTypeOptions} />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Mẫu hợp đồng"
              required
              rules={[{ required: true, message: 'Vui lòng chọn mẫu hợp đồng' }]}
            >
              <Select
                placeholder="Chọn mẫu hợp đồng"
                value={selectedTemplateId}
                onChange={handleTemplateChange}
                options={options?.template?.options?.map(item => ({
                  label: item.name,
                  value: item.id,
                }))}
              />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item label="Báo giá" name="quoteId" rules={[{ required: true, message: 'Vui lòng chọn báo giá' }]}>
              <Select
                placeholder="Chọn báo giá"
                options={options?.quote?.options?.map(item => ({
                  label: item.quotationNumber,
                  value: item.id,
                }))}
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item name="html" hidden>
          <Input />
        </Form.Item>

        <BaseDrawer
          title="Chi tiết hợp đồng"
          buttons={[
            {
              text: 'Đóng',
              onClick: onChildrenContractDrawerClose,
            },
          ]}
          open={childrenContractDrawer}
          onClose={onChildrenContractDrawerClose}
          width="50%"
        >
          <RichTextEditor
            value={editedHtml || ''}
            onChange={value => {
              setEditedHtml(value);
              form.setFieldValue('html', value);
            }}
            readOnly={false}
          />
        </BaseDrawer>

        <BaseDrawer
          title="Báo giá"
          buttons={[
            {
              text: 'Đóng',
              onClick: onChildrenQuoteDrawerClose,
            },
          ]}
          open={childrenQuoteDrawer}
          onClose={onChildrenQuoteDrawerClose}
          width="50%"
        >
          Báo giá
        </BaseDrawer>
      </Form>
    </BaseDrawer>
  );
}
