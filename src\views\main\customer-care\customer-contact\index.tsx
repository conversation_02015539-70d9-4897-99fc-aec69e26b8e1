// Marketing-campaign components

import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Col, Row } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { useState } from 'react';

import { IFilterCustomerContact } from '~/api/customer-care/complaint/types';
import { ICustomerContact } from '~/api/customer-care/customer-contact/types';
import BaseButton from '~/components/base-button';
import BaseCard from '~/components/base-card';
import BaseTable from '~/components/base-table';
import BaseView from '~/components/base-view';
import CreateCustomerSupportModal from './components/create-customer-support-modal';
import DetailButton from './components/detail-button';
import EditButton from './components/edit-button';
import FilterProduct from './components/filter-product';

export const CustomerContactView = () => {
  const [filter, setFilter] = useState<IFilterCustomerContact>({
    name: '',
    phone: '',
    email: '',
    pageIndex: 1,
    pageSize: 10,
  });
  const handleReset = () => {
    setFilter({
      name: '',
      phone: '',
      email: '',
      pageIndex: 1,
      pageSize: 10,
    });
  };
  const [visibleCreateModal, setVisibleCreateModal] = useState(false);

  const handleFilter = (values: IFilterCustomerContact) => {
    setFilter(values);
  };

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({
      ...filter,
      pageIndex: newPageIndex,
      pageSize: newPageSize,
    });
  };

  // fake data customer
  const [fakeData, setFakeData] = useState<ICustomerContact[]>([]);

  const handleDelete = (record: any) => {
    setFakeData(fakeData.filter(item => item.id !== record.id));
    // toastService.success('Xoá thành công', 3);
  };

  const columns: ColumnsType<ICustomerContact> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
    },
    {
      title: 'Tiêu đề',
      dataIndex: 'title',
      key: 'title',
      width: 150,
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description',
      width: 250,
    },
    {
      title: 'Loại',
      dataIndex: 'type',
      key: 'type',
      width: 100,
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 120,
    },
    {
      title: 'Địa điểm ghi thăm',
      dataIndex: 'visitLocation',
      key: 'visitLocation',
      width: 150,
    },
    {
      title: 'Mã khách hàng',
      dataIndex: 'customerCode',
      key: 'customerCode',
      width: 120,
    },
    {
      title: 'Mã SAP',
      dataIndex: 'sapCode',
      key: 'sapCode',
      width: 120,
    },
    {
      title: 'Địa chỉ',
      dataIndex: 'address',
      key: 'address',
      width: 200,
    },
    {
      title: 'NV theo dõi giám sát',
      dataIndex: 'supervisor',
      key: 'supervisor',
      width: 150,
    },
    {
      title: 'NV được phân công',
      dataIndex: 'assignedEmployee',
      key: 'assignedEmployee',
      width: 150,
    },
    {
      title: 'Mô đệ',
      dataIndex: 'model',
      key: 'model',
      width: 100,
    },
    {
      title: 'Ngày đến hạn',
      dataIndex: 'dueDate',
      key: 'dueDate',
      render: (value: string) => dayjs(value).format('DD/MM/YYYY'),
      width: 130,
    },
    {
      title: 'Ngày TH thực tế',
      dataIndex: 'actualDate',
      key: 'actualDate',
      render: (value: string) => dayjs(value).format('DD/MM/YYYY'),
      width: 130,
    },
    {
      title: 'Ngày check in',
      dataIndex: 'checkInDate',
      key: 'checkInDate',
      render: (value: string) => dayjs(value).format('DD/MM/YYYY HH:mm'),
      width: 150,
    },
    {
      title: 'Thời gian check out',
      dataIndex: 'checkOutTime',
      key: 'checkOutTime',
      render: (value: string) => dayjs(value).format('DD/MM/YYYY HH:mm'),
      width: 150,
    },
    {
      title: 'Trạng thái gửi tin',
      dataIndex: 'messageStatus',
      key: 'messageStatus',
      width: 150,
    },
    {
      title: 'Chức năng',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record: any) => (
        <>
          <DetailButton data={record} />
          <EditButton data={record} />
          <BaseButton
            danger
            type="primary"
            shape="circle"
            icon={<DeleteOutlined />}
            tooltip="Delete"
            onClick={() => handleDelete(record)}
          />
        </>
      ),
    },
  ];

  return (
    <BaseCard
      title="Danh sách thăm hỏi khách hàng"
      buttons={[
        {
          text: 'Thêm mới thăm hỏi',
          icon: <PlusOutlined />,
          onClick: () => {
            setVisibleCreateModal(true);
          },
          type: 'primary',
        },
      ]}
    >
      {visibleCreateModal && (
        <CreateCustomerSupportModal
          open={visibleCreateModal}
          onClose={() => setVisibleCreateModal(false)}
          onSuccess={() => setVisibleCreateModal(false)}
        />
      )}
      <BaseView>
        <Row gutter={16}>
          <Col span={24}>
            <FilterProduct onFilter={handleFilter} onReset={handleReset} isLoading={false} customerData={fakeData} />
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24} style={{ marginTop: 16 }}>
            <BaseTable columns={columns} data={fakeData} total={0} isLoading={false} scroll={{ x: 'max-content' }} />
          </Col>
        </Row>
      </BaseView>
    </BaseCard>
  );
};
