import type { ThemeConfig } from 'antd';

/**
 * 自定义的Ant Design浅色主题配置
 *
 * English: Custom Ant Design light theme configuration
 *
 * @see https://ant.design/theme-editor-cn (中文版)
 * @see https://ant.design/docs/react/customize-theme-cn (中文版配置指南)
 * @see https://ant.design/theme-editor (English version)
 * @see https://ant.design/docs/react/customize-theme (English version configuration guide)
 */
export const customAntdLightTheme: ThemeConfig = {};

/**
 * 自定义的Ant Design深色主题配置
 *
 * English: Custom Ant Design dark theme configuration
 *
 * @see https://ant.design/theme-editor-cn (中文版)
 * @see https://ant.design/docs/react/customize-theme-cn (中文版配置指南)
 * @see https://ant.design/theme-editor (English version)
 * @see https://ant.design/docs/react/customize-theme (English version configuration guide)
 */
export const customAntdDarkTheme: ThemeConfig = {};
