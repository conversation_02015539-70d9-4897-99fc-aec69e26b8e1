import { rootApiConnector } from '~/connectors';
import {
    IPermissionMemberCreate,
    IPermissionMemberFilter,
} from './types';

const ENDPOINT = {
    LIST_PERMISSION: 'api/client/permission/list',
    LIST_MEMBER_PERMISSION: 'api/client/permission/list-member-permission',
    UPDATE_PERMISSION: 'api/client/permission/create-member-permission',
};

class PermissionEmployeeApi {
    listPermission = (params: IPermissionMemberFilter) => {
        return rootApiConnector.get(ENDPOINT.LIST_PERMISSION, { params });
    };

    updatePermission = (body: IPermissionMemberCreate) => {
        return rootApiConnector.post(ENDPOINT.UPDATE_PERMISSION, body);
    };
}

export const permissionEmployeeApi = new PermissionEmployeeApi();
