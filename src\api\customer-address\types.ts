export interface CustomerAddressItemDto {
  id?: string;
  customerId?: string;
  address?: string;
  isDefault?: boolean;
  createdAt?: string;
  updatedAt?: string;
  isActive?: boolean;
}

export interface CustomerAddressListDto {
  data?: CustomerAddressItemDto[];
  total?: number;
}

export interface ListCustomerAddressDto extends CustomerAddressItemDto {
  pageIndex?: number;
  pageSize?: number;
}
