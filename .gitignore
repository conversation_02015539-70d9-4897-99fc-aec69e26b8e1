# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
/.pnp
.pnp.js

# testing
/coverage

# WebStorm
.idea

# production
/build
/dist

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# package lock
package-lock.json
yarn.lock
# pnpm-lock.yaml

# vitepress
**/.vitepress/dist
**/.vitepress/cache

Other
.eslintcache
analyzer
TODO.md

# dotenv environment variable files
.env
.env.*
!.env.example