import { EditOutlined, SaveOutlined, UploadOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd';
import { Button, Card, Col, DatePicker, Form, Input, InputNumber, message, Row, Select, Tag, Upload } from 'antd';
import { useForm } from 'antd/es/form/Form';
import dayjs from 'dayjs';
import { FC, useEffect, useState } from 'react';
import {
  ComplaintPriorityColors,
  ComplaintPriorityLabels,
  ComplaintStatusColors,
  ComplaintStatusLabels,
  ComplaintTypeLabels,
  IComplaint,
} from '~/api/customer-care/complaint/types';
import BaseButton from '~/components/base-button';
import BaseModal from '~/components/base-modal';
import BaseText from '~/components/base-text';

const { Option } = Select;
const { TextArea } = Input;

interface IProps {
  data: IComplaint;
}

// Dữ liệu mẫu cho khách hàng
const mockCustomers = [
  { id: '1', name: 'Công ty TNHH ABC' },
  { id: '2', name: 'Công ty TNHH XYZ' },
  { id: '3', name: '<PERSON>ông ty Cổ phần DEF' },
  { id: '4', name: 'Doanh nghiệp tư nhân GHI' },
];

// Dữ liệu mẫu cho tỉnh/thành phố
const mockProvinces = [
  { code: '01', name: 'Hồ Chí Minh' },
  { code: '02', name: 'Hà Nội' },
  { code: '03', name: 'Đà Nẵng' },
  { code: '04', name: 'Cần Thơ' },
];

// Dữ liệu mẫu cho quận/huyện
const mockDistricts = [
  { code: '001', name: 'Quận 1', provinceCode: '01' },
  { code: '002', name: 'Quận 2', provinceCode: '01' },
  { code: '003', name: 'Quận Ba Đình', provinceCode: '02' },
  { code: '004', name: 'Quận Hoàn Kiếm', provinceCode: '02' },
];

// Dữ liệu mẫu cho phường/xã
const mockWards = [
  { code: '00001', name: 'Phường Bến Nghé', districtCode: '001' },
  { code: '00002', name: 'Phường Bến Thành', districtCode: '001' },
  { code: '00003', name: 'Phường Thảo Điền', districtCode: '002' },
  { code: '00004', name: 'Phường An Phú', districtCode: '002' },
];

// Dữ liệu mẫu cho nhân viên
const mockEmployees = [
  { id: '1', name: 'Nguyễn Văn A' },
  { id: '2', name: 'Trần Thị B' },
  { id: '3', name: 'Lê Văn C' },
  { id: '4', name: 'Phạm Thị D' },
];

const EditButton: FC<IProps> = ({ data }: IProps) => {
  const { id } = data;
  const [open, setOpen] = useState(false);
  const [form] = useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedProvinceCode, setSelectedProvinceCode] = useState<string | null>(null);
  const [selectedDistrictCode, setSelectedDistrictCode] = useState<string | null>(null);

  const openModal = () => setOpen(true);
  const closeModal = () => setOpen(false);

  useEffect(() => {
    if (open && data) {
      form.setFieldsValue({
        title: data.title,
        description: data.description,
        type: data.type,
        priority: data.priority,
        customerId: data.customerId,
        complaintAddress: data.complaintAddress,
        address: data.address,
        provinceCode: data.provinceCode,
        districtCode: data.districtCode,
        wardCode: data.wardCode,
        supervisorId: data.supervisorId,
        assignedStaffId: data.assignedStaffId,
        startDate: data.startDate ? dayjs(data.startDate) : null,
        dueDate: data.dueDate ? dayjs(data.dueDate) : null,
        endDate: data.endDate ? dayjs(data.endDate) : null,
        checkinTime: data.checkinTime ? dayjs(data.checkinTime) : null,
        checkoutTime: data.checkoutTime ? dayjs(data.checkoutTime) : null,
        notes: data.notes,
        solution: data.solution,
        rating: data.rating,
        feedback: data.feedback,
      });

      // Cập nhật state cho tỉnh/thành phố và quận/huyện
      setSelectedProvinceCode(data.provinceCode || null);
      setSelectedDistrictCode(data.districtCode || null);

      // Set existing images to fileList
      if (data.media && data.media.length > 0) {
        const existingFiles: UploadFile[] = data.media.map((media, index) => ({
          uid: media.id || `-${index}`,
          name: media.name || `image-${index + 1}`,
          status: 'done',
          url: media.url,
        }));
        setFileList(existingFiles);
      } else {
        setFileList([]);
      }
    }
  }, [data, open, form]);

  if (!data) return null;

  const handleSave = async (values: any) => {
    if (!data) return;
    setIsSubmitting(true);

    const body = {
      ...values,
      id: data.id,
      startDate: values.startDate?.format('YYYY-MM-DD'),
      dueDate: values.dueDate?.format('YYYY-MM-DD'),
      endDate: values.endDate?.format('YYYY-MM-DD'),
      checkinTime: values.checkinTime?.format('YYYY-MM-DD HH:mm:ss'),
      checkoutTime: values.checkoutTime?.format('YYYY-MM-DD HH:mm:ss'),
      media: fileList.map(file => file.url),
    };

    // Giả lập gọi API
    setTimeout(() => {
      console.log('Dữ liệu khiếu nại đã cập nhật:', body);
      message.success('Cập nhật khiếu nại thành công');
      closeModal();
      // Reset form và file list
      form.resetFields();
      setFileList([]);
      setIsSubmitting(false);
      setSelectedProvinceCode(null);
      setSelectedDistrictCode(null);
    }, 1000);
  };

  // Xử lý khi chọn tỉnh/thành phố
  const handleProvinceChange = (value: string) => {
    setSelectedProvinceCode(value);
    setSelectedDistrictCode(null);
    form.setFieldsValue({ districtCode: undefined, wardCode: undefined });
  };

  // Xử lý khi chọn quận/huyện
  const handleDistrictChange = (value: string) => {
    setSelectedDistrictCode(value);
    form.setFieldsValue({ wardCode: undefined });
  };

  // Lọc quận/huyện theo tỉnh/thành phố đã chọn
  const filteredDistricts = selectedProvinceCode
    ? mockDistricts.filter(district => district.provinceCode === selectedProvinceCode)
    : [];

  // Lọc phường/xã theo quận/huyện đã chọn
  const filteredWards = selectedDistrictCode
    ? mockWards.filter(ward => ward.districtCode === selectedDistrictCode)
    : [];

  // Handle image upload
  const handleUploadChange = ({ fileList }: { fileList: UploadFile[] }) => {
    setFileList(fileList);
  };

  const handleRemove = (file: UploadFile) => {
    setFileList(prev => prev.filter(f => f.uid !== file.uid));
  };

  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('Chỉ được upload file hình ảnh!');
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('Hình ảnh phải nhỏ hơn 2MB!');
    }
    return isImage && isLt2M;
  };

  const uploadButton = (
    <div>
      <UploadOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );

  // Header thông tin khiếu nại
  const complaintHeader = (
    <Card style={{ marginBottom: 16 }}>
      <Row gutter={16}>
        <Col span={8}>
          <div>
            <BaseText color="textSecondary">Tiêu đề:</BaseText>
            <br />
            <BaseText weight="bold">{data.title || 'N/A'}</BaseText>
          </div>
        </Col>
        <Col span={8}>
          <div>
            <BaseText color="textSecondary">Loại khiếu nại:</BaseText>
            <br />
            <Tag color={ComplaintStatusColors[data.status]}>{ComplaintTypeLabels[data.type]}</Tag>
          </div>
        </Col>
        <Col span={8}>
          <div>
            <BaseText color="textSecondary">Trạng thái:</BaseText>
            <br />
            <Tag color={ComplaintStatusColors[data.status]}>{ComplaintStatusLabels[data.status]}</Tag>
          </div>
        </Col>
      </Row>
      <Row style={{ marginTop: 12 }}>
        <Col span={8}>
          <BaseText color="textSecondary">Mức độ ưu tiên:</BaseText>
          <br />
          <Tag color={ComplaintPriorityColors[data.priority]}>{ComplaintPriorityLabels[data.priority]}</Tag>
        </Col>
        <Col span={8}>
          <BaseText color="textSecondary">Hình ảnh:</BaseText>
          <br />
          <BaseText weight="bold">{data.media?.length || 0} ảnh</BaseText>
        </Col>
      </Row>
    </Card>
  );

  const modalContent = (
    <div>
      {complaintHeader}

      <Form form={form} layout="vertical" onFinish={handleSave}>
        {/* Thông tin cơ bản */}
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="Tiêu đề" name="title" rules={[{ required: true, message: 'Vui lòng nhập tiêu đề' }]}>
              <Input placeholder="Nhập tiêu đề khiếu nại" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Loại khiếu nại"
              name="type"
              rules={[{ required: true, message: 'Vui lòng chọn loại khiếu nại' }]}
            >
              <Select placeholder="Chọn loại khiếu nại">
                {Object.entries(ComplaintTypeLabels).map(([value, label]) => (
                  <Option key={value} value={value}>
                    {label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item label="Mô tả" name="description" rules={[{ required: true, message: 'Vui lòng nhập mô tả' }]}>
              <TextArea rows={3} placeholder="Nhập mô tả chi tiết khiếu nại" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Mức độ ưu tiên"
              name="priority"
              rules={[{ required: true, message: 'Vui lòng chọn mức độ ưu tiên' }]}
            >
              <Select placeholder="Chọn mức độ ưu tiên">
                {Object.entries(ComplaintPriorityLabels).map(([value, label]) => (
                  <Option key={value} value={value}>
                    {label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Mã khách hàng"
              name="customerId"
              rules={[{ required: true, message: 'Vui lòng chọn khách hàng' }]}
            >
              <Select placeholder="Chọn khách hàng">
                {mockCustomers.map(customer => (
                  <Option key={customer.id} value={customer.id}>
                    {customer.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        {/* Thông tin địa chỉ */}
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Địa chỉ khiếu nại"
              name="complaintAddress"
              rules={[{ required: true, message: 'Vui lòng nhập địa chỉ khiếu nại' }]}
            >
              <Input placeholder="Nhập địa chỉ khiếu nại" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Địa chỉ" name="address" rules={[{ required: true, message: 'Vui lòng nhập địa chỉ' }]}>
              <Input placeholder="Nhập địa chỉ" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item label="Tỉnh/Thành phố" name="provinceCode">
              <Select placeholder="Chọn tỉnh/thành phố" onChange={handleProvinceChange}>
                {mockProvinces.map(province => (
                  <Option key={province.code} value={province.code}>
                    {province.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Quận/Huyện" name="districtCode">
              <Select placeholder="Chọn quận/huyện" onChange={handleDistrictChange} disabled={!selectedProvinceCode}>
                {filteredDistricts.map(district => (
                  <Option key={district.code} value={district.code}>
                    {district.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Phường/Xã" name="wardCode">
              <Select placeholder="Chọn phường/xã" disabled={!selectedDistrictCode}>
                {filteredWards.map(ward => (
                  <Option key={ward.code} value={ward.code}>
                    {ward.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        {/* Thông tin nhân viên */}
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="NV theo dõi/giám sát"
              name="supervisorId"
              rules={[{ required: true, message: 'Vui lòng chọn nhân viên giám sát' }]}
            >
              <Select placeholder="Chọn nhân viên giám sát">
                {mockEmployees.map(employee => (
                  <Option key={employee.id} value={employee.id}>
                    {employee.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="NV được phân công"
              name="assignedStaffId"
              rules={[
                {
                  required: true,
                  message: 'Vui lòng chọn nhân viên được phân công',
                },
              ]}
            >
              <Select placeholder="Chọn nhân viên được phân công">
                {mockEmployees.map(employee => (
                  <Option key={employee.id} value={employee.id}>
                    {employee.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        {/* Thông tin thời gian */}
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Ngày bắt đầu"
              name="startDate"
              rules={[{ required: true, message: 'Vui lòng chọn ngày bắt đầu' }]}
            >
              <DatePicker style={{ width: '100%' }} placeholder="Chọn ngày bắt đầu" format="DD/MM/YYYY" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Ngày hết hạn"
              name="dueDate"
              rules={[{ required: true, message: 'Vui lòng chọn ngày hết hạn' }]}
            >
              <DatePicker style={{ width: '100%' }} placeholder="Chọn ngày hết hạn" format="DD/MM/YYYY" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="Ngày kết thúc" name="endDate">
              <DatePicker style={{ width: '100%' }} placeholder="Chọn ngày kết thúc" format="DD/MM/YYYY" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Thời gian check-in" name="checkinTime">
              <DatePicker
                style={{ width: '100%' }}
                placeholder="Chọn thời gian check-in"
                format="DD/MM/YYYY HH:mm:ss"
                showTime
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="Thời gian check-out" name="checkoutTime">
              <DatePicker
                style={{ width: '100%' }}
                placeholder="Chọn thời gian check-out"
                format="DD/MM/YYYY HH:mm:ss"
                showTime
              />
            </Form.Item>
          </Col>
        </Row>

        {/* Thông tin giải pháp và đánh giá */}
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item label="Ghi chú" name="notes">
              <TextArea rows={2} placeholder="Nhập ghi chú" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item label="Giải pháp" name="solution">
              <TextArea rows={2} placeholder="Nhập giải pháp" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="Đánh giá" name="rating">
              <InputNumber style={{ width: '100%' }} min={0} max={5} placeholder="Nhập đánh giá (0-5)" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Phản hồi" name="feedback">
              <TextArea rows={2} placeholder="Nhập phản hồi" />
            </Form.Item>
          </Col>
        </Row>

        {/* Hình ảnh đính kèm */}
        <Form.Item label="Hình ảnh đính kèm" name="media">
          <Upload
            listType="picture-card"
            fileList={fileList}
            beforeUpload={beforeUpload}
            multiple
            onChange={handleUploadChange}
            onRemove={handleRemove}
            accept="image/*"
            customRequest={({ file, onSuccess }) => {
              // Giả lập upload file
              setTimeout(() => {
                const fakeUrl = URL.createObjectURL(file as File);
                if (onSuccess) {
                  onSuccess({
                    Location: fakeUrl,
                    name: (file as File).name,
                  });
                }
              }, 500);
            }}
          >
            {fileList.length >= 8 ? null : uploadButton}
          </Upload>
          <div style={{ color: '#666', fontSize: '12px', marginTop: '8px' }}>
            Tối đa 8 hình ảnh, mỗi file nhỏ hơn 2MB
          </div>
        </Form.Item>

        <div
          style={{
            textAlign: 'right',
            marginTop: 24,
            borderTop: '1px solid #f0f0f0',
            paddingTop: 16,
          }}
        >
          <Button onClick={closeModal} style={{ marginRight: 8 }}>
            Hủy
          </Button>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={isSubmitting}>
            Cập nhật khiếu nại
          </Button>
        </div>
      </Form>
    </div>
  );

  return (
    <>
      <BaseButton type="primary" shape="circle" icon={<EditOutlined />} tooltip="Edit" onClick={openModal} />
      <BaseModal
        open={open}
        onClose={closeModal}
        title="Chỉnh sửa khiếu nại"
        description="Cập nhật thông tin khiếu nại"
        childrenBody={modalContent}
      />
    </>
  );
};

export default EditButton;
