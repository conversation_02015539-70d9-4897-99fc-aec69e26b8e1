import { SaveOutlined } from '@ant-design/icons';
import { Button, Checkbox, Col, DatePicker, Form, Input, Row, Select } from 'antd';
import BaseModal from '~/components/base-modal';

const { Option } = Select;

interface CreateCampaignModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export const CampaignCreateComponent = ({ open, onClose, onSuccess }: CreateCampaignModalProps) => {
  const [form] = Form.useForm();

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      console.log('Form values:', values);
      // Gọi API tạo mới ở đây nếu cần
      onSuccess?.();
      onClose();
      form.resetFields();
    } catch (error) {
      console.error('Validation Failed:', error);
    }
  };

  const contentModal = (
    <Form layout="vertical" form={form}>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="Mã chiến dịch"
            name="campaignCode"
            rules={[{ required: true, message: '<PERSON><PERSON> lòng nhập mã chiến dịch' }]}
          >
            <Input placeholder="Nhập mã chiến dịch" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="Tên chiến dịch"
            name="campaignName"
            rules={[{ required: true, message: 'Vui lòng nhập tên chiến dịch' }]}
          >
            <Input placeholder="Nhập tên chiến dịch" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="Nội dung"
            name="contentName"
            rules={[{ required: true, message: 'Vui lòng nhập tên nội dung' }]}
          >
            <Input placeholder="Nhập tên nội dung" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="Chi nhánh"
            name="groupName"
            rules={[{ required: true, message: 'Vui lòng nhập chi nhánh' }]}
          >
            <Select placeholder="Chọn chi nhánh">
              <Option value="C1000">Công ty Gỗ ABC</Option>
              <Option value="C1001">Công ty Gỗ XYZ</Option>
              <Option value="C1002">Công ty Gỗ DEF</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Nhóm" name="groupName" rules={[{ required: true, message: 'Vui lòng nhập tên nhóm' }]}>
            <Select placeholder="Chọn nhóm">
              <Option value="group1">Group 1</Option>
              <Option value="group2">Group 2</Option>
              <Option value="group3">Group 3</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Trạng thái" name="status" rules={[{ required: true, message: 'Chọn trạng thái' }]}>
            <Select placeholder="Chọn trạng thái">
              <Option value="sent">Sent</Option>
              <Option value="draft">Draft</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Lịch gửi" name="scheduledAt" rules={[{ required: true, message: 'Chọn thời gian gửi' }]}>
            <DatePicker showTime format="DD/MM/YYYY HH:mm:ss" style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      {/* Check box Gửi ngay lập tực */}
      <Form.Item name="sendImmediately" valuePropName="checked">
        <Checkbox>Gửi ngay lập tức</Checkbox>
      </Form.Item>
      <Row justify="center">
        <Button type="primary" icon={<SaveOutlined />} onClick={handleSubmit}>
          Tạo mới
        </Button>
      </Row>
    </Form>
  );

  return (
    <BaseModal
      open={open}
      title="Tạo chiến dịch mới"
      description="Thêm chiến dịch mới vào hệ thống"
      onClose={onClose}
      childrenBody={contentModal}
    />
  );
};
