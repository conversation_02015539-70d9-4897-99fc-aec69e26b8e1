import { useCallback, useEffect, useState } from 'react';
import { ICatalog, IListCatalog } from '~/api/setting/catalog/types';
import { catalogApi } from '~/api/setting/catalog';

export const useCatalogSelect = () => {
  const [data, setData] = useState<ICatalog[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [page, setPage] = useState<IListCatalog>({
    pageIndex: 1,
    pageSize: 50,
  });

  const loadData = useCallback(() => {
    setIsLoading(true);
    catalogApi
      .listActive(page)
      .then(result => {
        setData(result.data);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [page]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    data,
    setData,
    isLoading,
    setIsLoading,
    page,
    setPage,
  };
};
