import { Col, Row, Switch } from 'antd';
import { FC } from 'react';
import BaseTable from '~/components/base-table';
import BaseView from '~/components/base-view';
// import { useListCustomerAddress } from '~/hooks/customer-address/useListCustomerAddress';

interface IAddress {
  id: string;
  addressType: string;
  market: string;
  area: string;
  address: string;
  areaSize: number;
  note: string;
  isMain: boolean;
  createdBy: string;
  createdAt: string;
}

type IProps = {
  customerId: string;
};

export const AddressTab: FC<IProps> = (props: IProps) => {
  const { customerId } = props;
  // const { data, isLoading } = useListCustomerAddress({ customerId });
  const data = [];
  const isLoading = false;
  const columns: any = [
    {
      title: 'STT',
      dataIndex: 'stt',
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Tên địa chỉ',
      dataIndex: 'addressName',
      key: 'addressName',
      width: 200,
      align: 'center',
    },
    {
      title: 'Địa chỉ',
      dataIndex: 'address',
      key: 'address',
      width: 150,
      align: 'center',
    },
    {
      title: 'Địa chỉ chính',
      dataIndex: 'isDefault',
      key: 'isDefault',
      width: 120,
      align: 'center',
      render: (value: boolean) => <Switch checked={value} disabled />,
    },
    {
      title: 'Ghi chú',
      dataIndex: 'description',
      key: 'description',
      width: 150,
      align: 'center',
    },
  ];

  return (
    data && (
      <BaseView>
        <Row gutter={16}>
          <Col span={24} style={{ marginTop: 16 }}>
            <BaseTable columns={columns} data={data} total={data.length} isLoading={isLoading} scroll={{ x: 1300 }} />
          </Col>
        </Row>
      </BaseView>
    )
  );
};
