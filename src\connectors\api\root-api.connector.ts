import { createHttpClient } from '~/@core/network';
import { useAuthStore, usePreferencesStore } from '~/store';
import { AUTH_HEADER, LANG_HEADER } from '~/utils/request/constants';
import { handerError } from './helper';

export const rootApiConnector = createHttpClient({
  baseURL: import.meta.env.VITE_ROOT_API_BASE_URL,
  timeout: 2 * 60 * 1000,
  beforeRequest: config => {
    const { accessToken } = useAuthStore.getState();
    config.headers.set(AUTH_HEADER, `Bearer ${accessToken}`);
    // Language needs to be carried by all API interfaces
    config.headers.set(LANG_HEADER, usePreferencesStore.getState().language);
    return config;
  },
  handleError: err => {
    return handerError(err);
  },
  handleResponse: async res => {
    return res.data;
  },
});
