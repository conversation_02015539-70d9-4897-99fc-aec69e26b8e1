import { ContainerLayout } from '~/layout';
import type { AppRouteRecordRaw } from '~/router/types';

import { HomeOutlined } from '@ant-design/icons';
import { createElement } from 'react';
import { $t } from '~/locales';
import { CUSTOMER_CARE_MENU_ORDER } from '~/views/main/main-menu.order';
import { CUSTOMER_CARE_COMPLAINT_PATH, CUSTOMER_CARE_CONTACT_PATH, CUSTOMER_CARE_PATH } from './customer-care.path';
import { ComplaintView } from './customer-complaint';
import { CustomerContactView } from './customer-contact';

const routes: AppRouteRecordRaw[] = [
  {
    path: CUSTOMER_CARE_PATH,
    Component: ContainerLayout,
    handle: {
      order: CUSTOMER_CARE_MENU_ORDER.CUSTOMER_CARE,
      title: $t('common.menu.customer_care'),
      icon: createElement(HomeOutlined),
    },
    children: [
      {
        path: CUSTOMER_CARE_CONTACT_PATH,
        Component: CustomerContactView,
        handle: {
          title: $t('common.menu.customer_contact'),
          icon: createElement(HomeOutlined),
        },
      },
      {
        path: CUSTOMER_CARE_COMPLAINT_PATH,
        Component: ComplaintView,
        handle: {
          title: $t('common.menu.complaint'),
          icon: createElement(HomeOutlined),
        },
      },
    ],
  },
];

export default routes;
