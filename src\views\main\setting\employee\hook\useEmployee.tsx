import { useCallback, useEffect, useState } from 'react';
import { employeeApi } from '~/api/setting/employee';
import { IEmployee, IListEmployee } from '~/api/setting/employee/types';

export const useEmployee = () => {
  const [data, setData] = useState<IEmployee[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState<IListEmployee>({});

  const loadData = useCallback(async () => {
    setIsLoading(true);
    const result: any = await employeeApi.list(page);
    setData(Array.isArray(result.data) ? [...result.data] : []);
    setTotal(result.total);
    setIsLoading(false);
  }, [page]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    data,
    setData,
    isLoading,
    setIsLoading,
    total,
    setTotal,
    page,
    setPage,
    loadData,
  };
};
