{"accessMode": {"title": "<PERSON><PERSON><PERSON> th<PERSON> c<PERSON>", "description": "Trang hiện tại chỉ hỗ trợ lấy khả năng hiển thị tuyến đường thông qua giao diện backend"}, "adminVisible": {"title": "<PERSON><PERSON><PERSON> thị cho <PERSON>", "description": "<PERSON>rang hiện tại chỉ hiển thị cho tài kho<PERSON>n <PERSON>"}, "commonVisible": {"title": "<PERSON><PERSON><PERSON> th<PERSON>", "description": "Trang hiện tại chỉ hiển thị cho tài khoản Thông thường"}, "pageControl": {"alertMessage": "<PERSON><PERSON> cập <PERSON>", "alertDescription": "Chuyển đổi giữa các tài khoản khác nhau và quan sát các thay đổi trong menu con 'Trì<PERSON> diễn Quyền' ở bên trái.", "cardTitle": "<PERSON><PERSON> đ<PERSON>", "frontendControl": "<PERSON><PERSON><PERSON>", "backendControl": "<PERSON><PERSON><PERSON>", "switchToFrontend": "<PERSON><PERSON><PERSON><PERSON> sang <PERSON><PERSON> độ Quyền Frontend", "switchToBackend": "<PERSON><PERSON><PERSON><PERSON> sang <PERSON><PERSON> độ <PERSON>ền Backend", "switchAdmin": "<PERSON><PERSON><PERSON><PERSON> sang <PERSON><PERSON><PERSON><PERSON> dùng <PERSON>", "switchCommon": "<PERSON><PERSON><PERSON><PERSON> sang <PERSON><PERSON><PERSON>i dùng Thông thường", "currentPermissionMode": "<PERSON><PERSON> độ quyền hiện tại:", "accountSwitching": "Chuyển đổi <PERSON>n", "warningMessage": "<PERSON><PERSON>u enableFrontendAceess và enableBackendAceess bằng nhau, khô<PERSON> đư<PERSON><PERSON> phép chuyển đổi chế độ định tuyến."}, "buttonControl": {"alertMessage": "<PERSON><PERSON>", "alertDescription": "Chuyển đổi gi<PERSON>a các tài khoản khác nhau để quan sát thay đổi nút.", "currentRole": "<PERSON><PERSON> trò <PERSON> tại", "switchAdmin": "<PERSON><PERSON><PERSON><PERSON> sang <PERSON><PERSON><PERSON><PERSON> dùng <PERSON>", "switchCommon": "<PERSON><PERSON><PERSON><PERSON> sang <PERSON><PERSON><PERSON>i dùng Thông thường", "componentControlPermissionCodes": "<PERSON><PERSON><PERSON> so<PERSON>t dựa trên Thành phần - <PERSON><PERSON>", "componentControlRoles": "<PERSON><PERSON><PERSON> so<PERSON>t dựa trên Thành phần - <PERSON><PERSON> trò", "functionControlPermissionCodes": "<PERSON><PERSON><PERSON> so<PERSON>t dựa trên <PERSON> n<PERSON> - <PERSON><PERSON>", "functionControlRoles": "<PERSON><PERSON><PERSON> so<PERSON>t dựa trên <PERSON> năng - <PERSON>ai trò"}}