import { useCallback, useEffect, useState } from 'react';
import { IListEmployee, IEmployee } from '~/api/setting/employee/types';
import { permissionEmployeeApi } from '~/api/setting/permission';
import { IPermissionMemberCreate } from '~/api/setting/permission/types';

export const useEmployeePermission = ({ memberId }: { memberId: string }) => {
  const [dataPermissionResource, setDataPermissionResource] = useState<IEmployee[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState<IListEmployee>({
    pageIndex: 1,
    pageSize: 10,
  });

  const listPermissionResource = useCallback(async () => {
    setIsLoading(true);
    const result = await permissionEmployeeApi.listPermission({ ...page, memberId });

    const dataPermissionResource = result.data.map((item: any) => {
      return {
        id: item.id,
        name: item.name,
        canView: item.canView,
        canUpdate: item.canUpdate,
        canCreate: item.canCreate,
      };
    });
    setIsLoading(false);
    setDataPermissionResource(dataPermissionResource);
    setTotal(result.total);
  }, [page, memberId]);

  const updatePermissionMember = useCallback(async (body: IPermissionMemberCreate) => {
    setIsLoading(true);
    const result = await permissionEmployeeApi.updatePermission(body);
    setIsLoading(false);
    return result;
  }, []);

  useEffect(() => {
    if (memberId) {
      listPermissionResource();
    }
  }, [listPermissionResource, memberId]);

  return {
    dataPermissionResource,
    isLoading,
    setIsLoading,
    total,
    setTotal,
    page,
    setPage,
    updatePermissionMember,
  };
};
