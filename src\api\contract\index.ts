import { rootApiConnector } from '~/connectors';
import { IPageResponse } from '../@common';
import { IContract, ICreateContractDto, IPaginationContractDto } from './types';

const ENDPOINT = {
  PAGINATION: '/api/client/contract/pagination',
  CREATE: '/api/client/contract/create',
  UPDATE: '/api/client/contract/update',
};
class ContractApi {
  pagination = (query: IPaginationContractDto) => {
    return rootApiConnector.get<IPageResponse<IContract>>(ENDPOINT.PAGINATION, query);
  };

  create = (values: ICreateContractDto) => {
    return rootApiConnector.post(ENDPOINT.CREATE, values);
  };

  update = (values: IContract) => {
    return rootApiConnector.post(ENDPOINT.UPDATE, values);
  };
}
export const contractApi = new ContractApi();
