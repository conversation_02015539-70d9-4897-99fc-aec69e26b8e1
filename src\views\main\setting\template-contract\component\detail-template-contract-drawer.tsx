import { Col, Form, Input, Row } from 'antd';
import { useEffect } from 'react';
import { IContractTemplate } from '~/api/setting/contract-template/types';
import BaseDrawer from '~/components/base-drawer';
import RichTextEditor from '~/components/text-editor';

export default function DetailTemplateContractDrawer({
  open,
  onClose,
  data,
}: {
  open: boolean;
  onClose: () => void;
  data: IContractTemplate | null;
}) {
  const [form] = Form.useForm();

  useEffect(() => {
    if (data) {
      form.setFieldsValue({
        name: data.name,
        code: data.code,
        html: data.html,
      });
    }
  }, [data, form]);

  return (
    <BaseDrawer
      title="Chi tiết mẫu hợp đồng"
      buttons={[
        {
          text: 'Đóng',
          onClick: onClose,
        },
      ]}
      open={open}
      onClose={onClose}
      width="50%"
    >
      <Form layout="vertical" form={form} disabled>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="Tên mẫu" name="name">
              <Input placeholder="Nhập tên mẫu" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Mã hợp đồng" name="code">
              <Input placeholder="Nhập mã hợp đồng" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="Ngày tạo">
              <Input value={new Date(data?.createdDate || '').toLocaleString('vi-VN')} readOnly />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Cập nhật lần cuối">
              <Input value={new Date(data?.updatedDate || '').toLocaleString('vi-VN')} readOnly />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item label="Nội dung mẫu" name="html">
          <RichTextEditor value={form.getFieldValue('html') || ''} onChange={() => {}} readOnly={true} />
        </Form.Item>
      </Form>
    </BaseDrawer>
  );
}
