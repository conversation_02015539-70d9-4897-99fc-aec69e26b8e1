import { createContext, createElement } from 'react';
import { SSOLogin } from './containers/sso-login';

export const FORM_COMPONENT_MAP = {
  login: createElement(SSOLogin),
};

export type FormComponentMapType = keyof typeof FORM_COMPONENT_MAP;

export const FormModeContext = createContext<{
  formMode: FormComponentMapType;
  setFormMode: React.Dispatch<React.SetStateAction<FormComponentMapType>>;
}>({
  formMode: 'login',
  setFormMode: () => {},
});
