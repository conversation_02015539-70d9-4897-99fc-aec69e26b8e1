import { Col, Row } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { FC } from 'react';
import { ICustomerDetailContact } from '~/api/customer/types';
import BaseTable from '~/components/base-table';
import BaseView from '~/components/base-view';

type IProps = {
  data: ICustomerDetailContact[];
};

export const ContactTab: FC<IProps> = (props: IProps) => {
  const { data } = props;
  const columns: ColumnsType<ICustomerDetailContact> = [
    {
      title: 'STT',
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Tên',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      align: 'center',
    },
    {
      title: 'Số điện thoại',
      dataIndex: 'phone',
      key: 'phone',
      width: 150,
      align: 'center',
    },
    {
      title: '<PERSON>ứ<PERSON> vụ',
      dataIndex: 'position',
      key: 'position',
      width: 120,
      align: 'center',
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      width: 200,
      align: 'center',
    },
    {
      title: '<PERSON><PERSON><PERSON><PERSON> quyết định',
      dataIndex: 'isDecisionMaker',
      key: 'isDecisionMaker',
      width: 150,
      align: 'center',
      render: (value: boolean) => (value ? '✓' : ''),
    },
    {
      title: 'Người tạo',
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 150,
      align: 'center',
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdDate',
      key: 'createdDate',
      width: 150,
      align: 'center',
      render: (value: string) => (value ? new Date(value).toLocaleDateString('vi-VN') : '-'),
    },
  ];

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable columns={columns} data={data} total={data.length} isLoading={false} scroll={{ x: 1200 }} />
        </Col>
      </Row>
    </BaseView>
  );
};
