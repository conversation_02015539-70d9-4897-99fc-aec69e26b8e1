import type { UserInfoType } from '~/api/auth/types';
import { authApi } from '~/api/auth';

import { create } from 'zustand';

const initialState = {
  id: '',
  username: '',
  fullName: '',
  avatar: '',
  tenantId: '',
  tenantInfo: {
    id: '',
    name: '',
    domain: '',
    logoUrl: '',
    website: '',
    address: '',
  },
  roles: [],
  menus: [],
};

type AccountState = UserInfoType;

interface AccountAction {
  getUserInfo: () => Promise<UserInfoType>;
  reset: () => void;
}

export const useAccountStore = create<AccountState & AccountAction>()(set => ({
  ...initialState,

  getUserInfo: async () => {
    const response = await authApi.getUserInfo();
    set({
      ...response,
    });
    return response;
  },

  reset: () => {
    return set({
      ...initialState,
    });
  },
}));
