import { quotationApi } from '~/api/quotation';
import { IQuotation, IQuotationList, IQuotationUpdate } from '~/api/quotation/types';
import { UpdateStatusQuotationReq } from '~/api/sales-managerment/quotation-product/types';

export const useQuotationItem = () => {
  const listQuotationItem = (params: IQuotationList) => {
    return quotationApi.list(params);
  };

  const createQuotationItem = (data: IQuotation) => {
    return quotationApi.create(data);
  };

  const updateQuotationItem = (data: IQuotationUpdate) => {
    return quotationApi.update(data);
  };

  const detailQuotationItem = (id: string) => {
    return quotationApi.detail({ id });
  };
  const updateStatusQuotationItem = (data: UpdateStatusQuotationReq) => {
    return quotationApi.updateStatus(data);
  };

  return {
    listQuotationItem,
    createQuotationItem,
    updateQuotationItem,
    detailQuotationItem,
    updateStatusQuotationItem,
  };
};
