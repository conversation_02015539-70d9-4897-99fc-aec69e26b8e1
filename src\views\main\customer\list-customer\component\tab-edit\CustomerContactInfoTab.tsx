import { CloseOutlined, DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Card, notification } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useEffect, useState } from 'react';

import BaseConfirmButton from '~/components/base-confirm-button';
import BaseTable from '~/components/base-table';
import { ModalContact } from '../modal/modal-contact';
import useCustomerContact from '../../../Hooks/useCustomerContact';
import { IContact, IContactList, IContactUpdate } from '~/api/contact/types';

type IProps = {
  onClose: () => void;
  customerData: any;
};

export const CustomerContactInfoTab = (props: IProps) => {
  const { onClose, customerData } = props;

  const [contactsData, setContactsData] = useState<IContact[]>([]);
  const [total, setTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // State cho thông tin liên hệ
  const [contactModalOpen, setContactModalOpen] = useState(false);
  const [editingContact, setEditingContact] = useState<IContact | null>(null);
  const [contactForm] = useForm();

  const { createCustomerContact, updateCustomerContact, deleteCustomerContact, listCustomerContact } =
    useCustomerContact();

  useEffect(() => {
    loadData({});
  }, []);

  const loadData = (param: IContactList) => {
    setIsLoading(true);
    listCustomerContact({
      customerId: customerData.id,
      ...param,
    })
      .then(res => {
        setContactsData(res.data);
        setTotal(res.total);
        setIsLoading(false);
      })
      .catch(err => {
        setIsLoading(false);
      });
  };
  // Thêm mới contact
  const handleAddContact = () => {
    setEditingContact(null);
    contactForm.resetFields();
    setContactModalOpen(true);
  };

  // Sửa contact
  const handleEditContact = (contact: IContactUpdate) => {
    setEditingContact(contact);
    contactForm.setFieldsValue(contact);
    setContactModalOpen(true);
  };

  // Xóa contact
  const handleDeleteContact = async (contactId: string) => {
    setIsLoading(true);
    await deleteCustomerContact(contactId)
      .then(res => {
        if (res) {
          loadData({});
          notification.success({
            message: res.message,
            placement: 'top',
          });
        }
      })
      .catch(err => {
        notification.error({
          message: err.message,
          placement: 'top',
        });
        loadData({});
      });
    setIsLoading(false);
  };

  // Lưu contact (thêm mới hoặc cập nhật)
  const handleSaveContact = async (values: any) => {
    setIsLoading(true);
    const formattedValues = {
      customerId: customerData.id,
      ...editingContact,
      ...values,
    };
    if (!editingContact) {
      await createCustomerContact(formattedValues)
        .then((res: any) => {
          if (res) {
            loadData({});
            setContactModalOpen(false);
            notification.success({
              message: res.message,
              placement: 'top',
            });
          }
        })
        .catch(err => {
          notification.error({
            message: err.message,
            placement: 'top',
          });
          loadData({});
        });
    } else {
      await updateCustomerContact(formattedValues)
        .then((res: any) => {
          if (res) {
            loadData({});
            notification.success({
              message: res.message,
              placement: 'top',
            });
          }
        })
        .catch(err => {
          notification.error({
            message: err.message,
            placement: 'top',
          });
          loadData({});
        });
    }

    setContactModalOpen(false);
    setIsLoading(false);
  };

  const columns: any = [
    {
      title: 'STT',
      dataIndex: 'stt',
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Tên người liên hệ',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      align: 'center',
    },
    {
      title: 'Chức vụ',
      dataIndex: 'position',
      key: 'position',
      width: 150,
      align: 'center',
    },
    {
      title: 'Người quyết định',
      dataIndex: 'isDecisionMaker',
      key: 'isDecisionMaker',
      width: 120,
      align: 'center',
      render: (value: boolean) => (value ? '✓' : ''),
    },
    {
      title: 'Số điện thoại',
      dataIndex: 'phone',
      key: 'phone',
      width: 150,
      align: 'center',
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      width: 200,
      align: 'center',
    },
    {
      title: 'Ghi chú',
      dataIndex: 'note',
      key: 'note',
      width: 150,
      align: 'center',
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 100,
      align: 'center',
      render: (_, record: any) => (
        <div style={{ display: 'flex', gap: 8, justifyContent: 'center' }}>
          <Button type="primary" icon={<EditOutlined />} onClick={() => handleEditContact(record)} size="middle" />

          <BaseConfirmButton
            icon={<DeleteOutlined />}
            tooltip="Delete"
            danger
            confirmTitle="Bạn có chắc muốn xóa?"
            onConfirm={() => handleDeleteContact(record?.id)}
            size="middle"
            type="default"
          />
        </div>
      ),
    },
  ];

  return (
    <div>
      <ModalContact
        editingContact={editingContact}
        contactModalOpen={contactModalOpen}
        setContactModalOpen={setContactModalOpen}
        contactForm={contactForm}
        handleSaveContact={handleSaveContact}
      />
      <Card
        title="Thông tin liên hệ"
        extra={
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAddContact}>
            Thêm mới
          </Button>
        }
        style={{ marginTop: 16 }}
      >
        <BaseTable columns={columns} data={contactsData} total={total} isLoading={isLoading} />
      </Card>

      <div
        style={{
          textAlign: 'right',
          marginTop: 24,
          borderTop: '1px solid #f0f0f0',
          paddingTop: 16,
        }}
      >
        <Button
          danger
          htmlType="button"
          icon={<CloseOutlined style={{ fontSize: 12 }} />}
          style={{ marginRight: 8 }}
          onClick={onClose}
        >
          Đóng
        </Button>
      </div>
    </div>
  );
};
