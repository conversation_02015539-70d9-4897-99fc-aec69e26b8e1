import { IPageResponse } from '~/api/@common';
import { rootApiConnector } from '~/connectors';
import { IContractTemplate, ICreateContractTemplate, IPaginationContractTemplate } from './types';

const ENDPOINT = {
  PAGINATION: '/api/client/contract/pagination-template',
  DETAIL: '/api/client/contract/detail-template',
  CREATE: '/api/client/contract/create-template',
  UPDATE: '/api/client/contract/update-template',
  INACTIVE: '/api/client/contract/inactive-template',
  ACTIVE: '/api/client/contract/active-template',
};
class ContractTemplateApi {
  pagination = (query: IPaginationContractTemplate) => {
    return rootApiConnector.get<IPageResponse<IContractTemplate>>(ENDPOINT.PAGINATION, query);
  };

  create = (body: ICreateContractTemplate) => {
    return rootApiConnector.post<IContractTemplate>(ENDPOINT.CREATE, body);
  };

  update = (body: IContractTemplate) => {
    return rootApiConnector.post<IContractTemplate>(ENDPOINT.UPDATE, body);
  };

  inactive = (id: string) => {
    return rootApiConnector.post<IContractTemplate>(ENDPOINT.INACTIVE + `/${id}`);
  };

  active = (id: string) => {
    return rootApiConnector.post<IContractTemplate>(ENDPOINT.ACTIVE + `/${id}`);
  };

  detailTemplate = (id: string) => {
    return rootApiConnector.get<IContractTemplate>(ENDPOINT.DETAIL + `/${id}`);
  };
}
export const contractTemplateApi = new ContractTemplateApi();
