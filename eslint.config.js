import antfu from '@antfu/eslint-config';

export default antfu({
  react: true,
  markdown: false,
  ignorePatterns: ['*.md', '*.mdx', 'docs/**/*', '**/docs/**/*', '**/*.md'],
  type: 'lib',
  stylistic: {
    indent: 2,
  },
  yaml: false,
  rules: {
    // Style rules
    'style/quotes': 'off',
    'style/semi': 'off',
    'jsonc/indent': 'off',
    'style/no-tabs': 'off',
    'style/arrow-parens': 'off',
    'style/jsx-indent-props': 'off',
    'style/comma-dangle': 'off',
    'style/multiline-ternary': 'off',
    'style/no-mixed-spaces-and-tabs': 'off',
    'style/operator-linebreak': 'off',
    'style/quote-props': 'off',
    'style/brace-style': 'off',
    'style/jsx-curly-newline': 'off',
    'antfu/consistent-list-newline': 'off',
    'antfu/if-newline': 'off',
    'style/indent': 'off',
    'style/member-delimiter-style': 'off',
    'perfectionist/sort-imports': 'off',
    'import/no-mutable-exports': 'off',
    'style/jsx-wrap-multilines': 'off',
    'style/indent-binary-ops': 'off',
    'no-console': 'warn',
    'perfectionist/sort-named-imports': 'off',
    'prefer-template': 'off',
    'regexp/prefer-d': 'off',
    'regexp/no-super-linear-backtracking': 'off',
    'jsx-one-expression-per-line': 'off',
    'antfu/curly': 'off',
    'style/spaced-comment': 'off',
    'regexp/no-unused-capturing-group': 'off',
    'style/jsx-one-expression-per-line': 'off',
    'unicorn/prefer-number-properties': 'off',
    'ts/no-unused-vars': 'off',
    'unused-imports/no-unused-vars': 'off',
    'ts/no-use-before-define': 'off',

    // typescript rules
    '@typescript-eslint/consistent-type-imports': 'off',

    'ts/explicit-function-return-type': 'off',
    '@typescript-eslint/consistent-type-definitions': 'off',
    '@typescript-eslint/no-namespace': 'off',
    // antfu rules
    'antfu/top-level-function': 'off',

    // React hooks rules
    'react-hooks-extra/no-direct-set-state-in-use-effect': 'off',
    'react-hooks-extra/prefer-use-state-lazy-initialization': 'off',
    'react-hooks-extra/no-unnecessary-use-prefix': 'off',
    'react-hooks/exhaustive-deps': 'off',

    // JSDoc rules
    'jsdoc/require-returns-check': 'off',
    'jsdoc/check-param-names': 'off',

    // React DOM rules
    'react-dom/no-missing-button-type': 'off',
    'react-dom/no-missing-iframe-sandbox': 'off',

    // React rules
    'react/no-clone-element': 'off',
    'react-refresh/only-export-components': 'off',
  },
});
