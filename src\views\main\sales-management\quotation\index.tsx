import {
  CheckOutlined,
  CloseOutlined,
  DeleteOutlined,
  EyeOutlined,
  PlusOutlined,
  SendOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Button, Col, Row, Select, Space, Tag } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router';
import { IQuotationList } from '~/api/quotation/types';
import { QuoteStatus, QuoteStatusOptions } from '~/common/enums/quote.enum';
import { formatDate, formatMoneyVND } from '~/common/helpers/helper';
import BaseButton from '~/components/base-button';
import BaseCard from '~/components/base-card';
import BaseConfirmButton from '~/components/base-confirm-button';
import BaseFilter from '~/components/base-filter/BaseFilter';
import BaseTable from '~/components/base-table';
import CreateQuotationModal from './components/quotation-create-component';
import QuotationEditComponent from './components/quotation-edit-component';
import { useFilter } from './Hooks/useFilter';
import { useQuotation } from './Hooks/useQuotation';

const { Option } = Select;

// Dữ liệu mẫu cho danh sách báo giá

// Dữ liệu mẫu cho các trường lọc

export const QuotationsComponent = () => {
  const navigate = useNavigate();

  const [quoteData, setQuoteData] = useState([]);
  const [customerData, setCustomerData] = useState([]);
  const [total, setTotal] = useState(0);
  const { filterFields, filterData, handleFilter, handleFilterReset } = useFilter();
  const [quotes, setQuotes] = useState(quoteData);
  const [isLoading, setIsLoading] = useState(false);
  const [isPendingUpdateStatus, setIsPendingUpdateStatus] = useState(false);
  const [visibleCreateModal, setVisibleCreateModal] = useState<boolean>(false);

  const { listQuotation, createQuotation, updateQuotation, detailQuotation, updateStatusQuotation } = useQuotation();

  useEffect(() => {
    loadData(filterData);
  }, [filterData]);

  const initData = async () => {};

  const loadData = (params: IQuotationList) => {
    setIsLoading(true);
    if (params.quotationDate) {
      params.quotationDateFrom = dayjs(params.quotationDate[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss');
      params.quotationDateTo = dayjs(params.quotationDate[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss');
      delete params.quotationDate;
    }
    listQuotation(params)
      .then(res => {
        setQuotes(res.data);
        setTotal(res.total);
        setIsLoading(false);
      })
      .catch(err => {
        setIsLoading(false);
      });
  };

  // Xử lý lọc dữ liệu

  const handleDetailQuotation = (id: string) => {
    navigate(`detail?id=${id}`);
  };

  // Xử lý cập nhật trạng thái báo giá
  const updateStatus = ({ id, status }: { id: string; status: QuoteStatus }) => {
    setIsPendingUpdateStatus(true);
    setIsLoading(true);
    updateStatusQuotation({ id, status })
      .then(res => {
        if (res) {
          loadData(filterData);
        }
      })
      .catch(() => {
        loadData(filterData);
      })
      .finally(() => {
        setIsPendingUpdateStatus(false);
        setIsLoading(false);
      });
  };

  const handleSendQuotation = (id: string) => {
    if (isPendingUpdateStatus) return;
    updateStatus({ id, status: QuoteStatus.SENT });
    // Có thể thêm thông báo thành công ở đây
  };

  const handleDeleteQuotation = (id: string) => {
    if (isPendingUpdateStatus) return;
    updateStatus({ id, status: QuoteStatus.CANCELLED });
    // Có thể thêm thông báo thành công ở đây
  };

  const handleApproveQuotation = (id: string) => {
    if (isPendingUpdateStatus) return;
    updateStatus({ id, status: QuoteStatus.APPROVED });
    // Có thể thêm thông báo thành công ở đây
  };

  const handleCustomerAccept = (id: string) => {
    if (isPendingUpdateStatus) return;
    updateStatus({ id, status: QuoteStatus.CONFIRMED });
    // Có thể thêm thông báo thành công ở đây
  };

  const handleCustomerReject = (id: string) => {
    if (isPendingUpdateStatus) return;
    updateStatus({ id, status: QuoteStatus.REJECTED });
    // Có thể thêm thông báo thành công ở đây
  };

  const renderActions = (record: any) => {
    switch (record.status) {
      case QuoteStatus.NEW:
        return (
          <Space wrap>
            <QuotationEditComponent data={record} onSuccess={() => loadData(filterData)} />
            <BaseConfirmButton
              danger
              type="default"
              size="middle"
              icon={<DeleteOutlined />}
              onConfirm={() => handleDeleteQuotation(record.id)}
              tooltip="Hủy báo giá"
              confirmTitle="Bạn có chắc chắn muốn hủy báo giá này không?"
            />
            <BaseConfirmButton
              type="primary"
              size="middle"
              icon={<CheckOutlined />}
              onConfirm={() => handleApproveQuotation(record.id)}
              tooltip="Duyệt báo giá"
              confirmTitle="Bạn có chắc chắn muốn duyệt báo giá này không?"
            />
          </Space>
        );

      case QuoteStatus.CANCELLED:
        return (
          <BaseButton
            type="primary"
            icon={<EyeOutlined />}
            onClick={() => handleDetailQuotation(record.id)}
            tooltip="Xem chi tiết"
          />
        );

      case QuoteStatus.APPROVED:
        return (
          <Space wrap>
            <Button
              type="primary"
              size="middle"
              icon={<EyeOutlined />}
              onClick={() => handleDetailQuotation(record.id)}
            />
            <BaseConfirmButton
              type="primary"
              size="middle"
              icon={<SendOutlined />}
              onConfirm={() => handleSendQuotation(record.id)}
              tooltip="Gửi khách hàng"
              confirmTitle="Bạn có chắc chắn muốn gửi khách hàng này không?"
            />
          </Space>
        );

      case QuoteStatus.SENT:
        return (
          <Space wrap>
            <BaseConfirmButton
              type="primary"
              size="middle"
              icon={<CloseOutlined />}
              onConfirm={() => handleCustomerReject(record.id)}
              tooltip="KH Từ chối"
              confirmTitle="Bạn có chắc chắn muốn từ chối báo giá này không?"
            />
            <BaseConfirmButton
              type="primary"
              size="middle"
              icon={<UserOutlined />}
              onConfirm={() => handleCustomerAccept(record.id)}
              tooltip="KH Chốt"
              confirmTitle="Bạn có chắc chắn muốn chốt báo giá này không?"
            />
          </Space>
        );

      case QuoteStatus.CONFIRMED:
        return (
          <BaseButton
            type="primary"
            icon={<EyeOutlined />}
            onClick={() => handleDetailQuotation(record.id)}
            tooltip="Xem chi tiết"
          />
        );
      case QuoteStatus.REJECTED:
        return (
          <BaseButton
            type="primary"
            icon={<EyeOutlined />}
            onClick={() => handleDetailQuotation(record.id)}
            tooltip="Xem chi tiết"
          />
        );

      default:
        return null;
    }
  };

  const columns = [
    {
      title: 'STT',
      dataIndex: 'stt',
      align: 'center' as const,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: 'Số báo giá',
      dataIndex: 'quotationNumber',
    },
    {
      title: 'Khách hàng',
      dataIndex: 'customerName',
    },
    {
      title: 'Giá trị',
      dataIndex: 'totalAmount',
      render: (value: number) => {
        const totalValue = Math.round(value);
        return formatMoneyVND(totalValue);
      },
    },
    {
      title: 'Ngày báo giá',
      dataIndex: 'quotationDate',
      render: (date: string) => formatDate(date),
    },
    {
      title: 'Hạn báo giá',
      dataIndex: 'quotationDate',
      render: (date: string, record: any) => {
        if (!record.quotationDate || !record.validityDays) {
          return '-';
        }

        const expiryDate = dayjs(record.quotationDate).add(record.validityDays, 'days').format('DD/MM/YYYY - HH:mm');

        return expiryDate;
      },
    },
    {
      title: 'Người tạo',
      dataIndex: 'createdByName',
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      render: (status: QuoteStatus) => (
        <Tag
          color={QuoteStatusOptions[status]?.colorTag}
          style={{
            fontSize: '14px',
            padding: '4px 12px',
            width: '100%',
            textAlign: 'center',
          }}
        >
          {QuoteStatusOptions[status]?.label}
        </Tag>
      ),
    },
  ];

  const actionColumn: ColumnsType<any> = [
    {
      title: 'Tác vụ',
      fixed: 'right' as const,
      render: (record: any) => renderActions(record),
    },
  ];

  return (
    <BaseCard
      title="Quản lý báo giá"
      buttons={[
        {
          text: 'Thêm mới báo giá',
          icon: <PlusOutlined />,
          onClick: () => {
            setVisibleCreateModal(true);
          },
          type: 'primary',
        },
      ]}
    >
      {visibleCreateModal && (
        <CreateQuotationModal
          open={visibleCreateModal}
          onClose={() => setVisibleCreateModal(false)}
          onSuccess={() => {
            setVisibleCreateModal(false);
            loadData(filterData);
          }}
        />
      )}
      <BaseFilter onReset={handleFilterReset} isLoading={isLoading} filters={filterFields} onFilter={handleFilter} />
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={[...columns, ...actionColumn]}
            data={quotes}
            total={quotes.length}
            isLoading={isLoading}
            scroll={{ x: 'max-content' }}
          />
        </Col>
      </Row>
    </BaseCard>
  );
};
