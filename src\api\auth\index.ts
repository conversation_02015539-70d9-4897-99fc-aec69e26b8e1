import { rootApiConnector } from '~/connectors';
import { apiHelper } from '../@common/api.helper';
import type { AuthType, ForgotPasswordReq, PasswordLoginReq, ResetPasswordReq, UserInfoType } from './types';

export * from './types';

export const refreshTokenPath = 'refresh-token';
const ENDPOINT = {
  LOGIN: 'api/client/auth/login',
  GET_ASYNC_ROUTES: 'api/get-async-routes',
  GET_USER_INFO: 'api/client/auth/userinfo',
  REFRESH_TOKEN: 'api/refresh-token',
  FORGOT_PASSWORD: 'api/client/auth/forgot-password',
  RESET_PASSWORD: 'api/client/auth/reset-password',
  SSO_LOGIN: 'api/client/auth/ape',
};

class AuthApi {
  login = (body: PasswordLoginReq) => {
    return rootApiConnector
      .post<AuthType>(ENDPOINT.LOGIN, {
        ...body,
        clientId: import.meta.env.VITE_SSO_CLIENT_ID,
        clientSecret: import.meta.env.VITE_SSO_CLIENT_SECRET,
        redirectUri: import.meta.env.VITE_SSO_REDIRECT_URI,
      })
      .then(apiHelper.pipeSnakeCaseToCamelCase);
  };

  forgotPassword = (body: ForgotPasswordReq) => {
    return rootApiConnector.post(ENDPOINT.FORGOT_PASSWORD, body);
  };

  resetPassword = (body: ResetPasswordReq) => {
    return rootApiConnector.post(ENDPOINT.RESET_PASSWORD, body);
  };

  getUserInfo = async () => {
    const userInfo = await rootApiConnector.get<UserInfoType>(ENDPOINT.GET_USER_INFO);
    return {
      ...userInfo,
      avatar: userInfo.avatar || 'https://avatars.githubusercontent.com/u/47056890',
    };
  };

  ssoLogin = async () => {
    const apiUrl = import.meta.env.VITE_ROOT_API_BASE_URL;
    const redirectUri = encodeURIComponent(`${window.location.origin}/ape-callback`);
    window.location.href = `${apiUrl}/api/client/auth/ape?redirectUri=${redirectUri}`;
  };
}
export const authApi = new AuthApi();
