import { IPageRequest } from '../@common';

export interface IQuotation {
  id?: string;
  customerId?: string;
  memberId?: string;
  companyName?: string;
  companyAddress?: string;
  companyTaxCode?: string;
  companyContactPerson?: string;
  companyPhone?: string;
  companyEmail?: string;
  quotationNumber?: string;
  quotationDate?: string;
  customerName?: string;
  customerAddress?: string;
  customerTaxCode?: string;
  contacts?: string;
  customerPhone?: string;
  deliveryDate?: string;
  deliveryLocation?: string;
  paymentMethod?: string;
  validityDays?: number;
  notes?: string;
  quotationProducts?: IQuotationProduct[];
  totalAmount?: number;
  quoteItems?: IQuotationProduct[];
}

export interface IQuotationUpdate extends IQuotation {
  id: string;
}

export interface IQuotationResponse {
  data: IQuotation[];
  total: number;
}

export interface IQuotationList extends IPageRequest {
  quotationNumber?: string;
  customerId?: string;
  customerName?: string;
  q?: string;
  status?: string;
  createDate?: string[];
  createDateFrom?: string;
  createDateTo?: string;
  quotationDate?: string[];
  quotationDateFrom?: string;
  quotationDateTo?: string;
}

export interface IQuotationProduct {
  id: string;
  tenantId: any;
  createdDate: string;
  updatedDate: string;
  createdBy: any;
  updatedBy: any;
  code: string;
  name: string;
  type: string;
  description: any;
  unit: string;
  currency: string;
  unitPrice: string;
  taxRate: string;
  images: any;
  attachments: any;
  status: string;
  quantity: number;
  totalBeforeVat: number;
  totalAfterVat: number;
}
