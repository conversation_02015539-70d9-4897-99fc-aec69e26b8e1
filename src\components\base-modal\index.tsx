import { EditOutlined } from '@ant-design/icons';
import { Button, Modal, Select, Space, Typography } from 'antd';
import React from 'react';
import useScreenSize from '~/hooks/use-screen-size';
import BaseText from '../base-text';

const { Title, Paragraph } = Typography;
const { Option } = Select;

interface BaseModalProps {
  open: boolean;
  title: string;
  description?: string;
  onClose?: () => void;
  onEdit?: () => void;
  childrenBody: React.ReactNode;
  width?: number;
}
const BaseModal: React.FC<BaseModalProps> = ({ open, title, description, onClose, onEdit, childrenBody, width }) => {
  const { width: screenWidth } = useScreenSize();
  const headerStyle = {
    borderBottom: '1px solid #f0f0f0',
    padding: '16px 24px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  };

  return (
    <Modal open={open} onCancel={onClose} footer={null} width={width || screenWidth / 1.2} centered destroyOnClose>
      {/* Header */}
      <div style={headerStyle}>
        <div>
          <Title level={4} style={{ margin: 0, fontSize: 18, fontWeight: 'bold' }}>
            {title}
          </Title>
          <BaseText color="textSecondary">{description}</BaseText>
        </div>
        {onEdit && (
          <Space>
            <Button icon={<EditOutlined />} onClick={onEdit} type="text">
              Edit
            </Button>
          </Space>
        )}
      </div>

      {/* Body */}
      <div
        style={{
          padding: '24px',
          maxHeight: '70vh',
          overflowY: 'auto' as const,
        }}
      >
        {childrenBody}
      </div>
    </Modal>
  );
};

export default BaseModal;
